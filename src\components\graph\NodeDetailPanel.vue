<template>
  <div class="node-detail-panel">
    <div class="panel-header">
      <input type="text" v-model="title" class="title-editor" placeholder="知识点标题" />
      <div class="panel-actions">
        <button class="action-button" @click="saveChanges">保存</button>
        <button class="action-button cancel-button" @click="cancelEditing">取消</button>
        <button class="close-button" @click="close">×</button>
      </div>
    </div>
    <div class="panel-content">
      <div class="node-content">
        <div class="content-header">
          <h4>编辑内容</h4>
          <button class="search-button" @click="searchContent">
            <span class="search-icon">🤖</span> AI搜索
          </button>
        </div>
        <textarea 
          v-model="content" 
          class="content-editor"
          placeholder="请输入知识点内容..."
        ></textarea>
        <div v-if="isSearching" class="search-loading">
          <div class="loading-spinner search-spinner"></div>
          <span>正在搜索相关内容...</span>
        </div>
      </div>
      <!-- <div class="node-relations">
        <h4>关联知识点</h4>
        <ul v-if="relatedNodes.length > 0">
          <li v-for="node in relatedNodes" :key="node.id" @click="selectRelatedNode(node)">
            {{ node.text }} <span class="relation-type">({{ node.relationType }})</span>
          </li>
        </ul>
        <p v-else>暂无关联知识点</p>
      </div> -->
      <!-- <div class="node-resources">
        <h4>相关资料</h4>
        <div v-if="isImageSearching" class="search-loading">
          <div class="loading-spinner search-spinner"></div>
          <span>正在搜索相关图片...</span>
        </div>
        <div v-if="imageResult" class="image-result">
          <img :src="imageResult" alt="相关图片" class="resource-image" />
          <div class="image-actions">
            <button class="save-image-button" @click="saveImageAsResource">保存为资料</button>
          </div>
        </div>
        <ul v-if="resources.length > 0">
          <li v-for="resource in resources" :key="resource.id">
            <a :href="resource.url" target="_blank" class="resource-link">
              {{ resource.title }}
              <span class="resource-type">({{ resource.type }})</span>
            </a>
          </li>
        </ul>
        <p v-else>暂无相关资料</p>
        <div class="resource-actions">
          <button class="search-button" @click="searchImage">
            <span class="search-icon">🔍</span> 图片搜索
          </button>
          <button class="add-resource-button" @click="addResource">
            <span class="add-icon">+</span> 添加资料
          </button>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

// Define interfaces for props
interface Node {
  id: string | number;
  text: string;
  content?: string;
  resources?: any[];
  [key: string]: any;
}

interface RelatedNode {
  id: string | number;
  text: string;
  relationType?: string;
  content?: string;
}

// Define props
const props = defineProps<{
  node: Node,
  relatedNodes: RelatedNode[]
}>();

// Define emits
const emit = defineEmits(['save', 'cancel', 'close', 'select-related']);

// Component state
const title = ref('');
const content = ref('');
const isSearching = ref(false);
const isImageSearching = ref(false);
const imageResult = ref<string | null>(null);
const resources = ref<any[]>([]);

// Watch for changes in the node prop
watch(() => props.node, (newNode) => {
  if (newNode) {
    title.value = newNode.name || '';
    content.value = newNode.content || '';
    resources.value = newNode.resources || [];
    imageResult.value = null; // Reset image result on node change
  }
}, { immediate: true, deep: true });

// Methods
const saveChanges = () => {
  console.log('saveChanges', title.value, content.value);
  emit('save', {
    id: props.node.id,
    text: title.value,
    content: content.value,
  });
};

const cancelEditing = () => {
  // Revert changes by re-watching the prop
  if (props.node) {
    title.value = title.value || '';
    content.value = content.value|| '';
  }
  emit('cancel');
};

const close = () => {
  emit('close');
};

const selectRelatedNode = (node: RelatedNode) => {
  emit('select-related', node);
};

const addResource = () => {
  const resourceTitle = prompt('请输入资料标题:', '');
  if (resourceTitle) {
    const resourceUrl = prompt('请输入资料链接:', 'https://');
    if (resourceUrl) {
      const resourceType = prompt('请输入资料类型 (如: 论文/教材/视频):', '参考资料');
      if (resourceType) {
        resources.value.push({
          id: Date.now().toString(),
          title: resourceTitle,
          url: resourceUrl,
          type: resourceType
        });
      }
    }
  }
};

const searchImage = async () => {
  if (!title.value.trim()) {
    alert('请先输入知识点标题');
    return;
  }

  isImageSearching.value = true;
  imageResult.value = null;

  try {
    const searchTerm = encodeURIComponent(title.value);
    const apiKey = 'demo_key'; // Replace with your actual API key
    const apiUrl = `https://cn.apihz.cn/api/img/apihzimgbaidu.php?key=${apiKey}&words=${searchTerm}&limit=1&type=1`;
    
    const response = await fetch(apiUrl);
    const data = await response.json();

    if (data?.code === 200 && data.data?.length > 0) {
      imageResult.value = data.data[0].url;
    } else {
      console.error('Baidu Image API error:', data);
      imageResult.value = `https://placehold.co/400x300?text=${encodeURIComponent(title.value)}`;
    }
  } catch (error) {
    console.error('Image search failed:', error);
    imageResult.value = `https://placehold.co/400x300?text=${encodeURIComponent(title.value)}`;
  } finally {
    isImageSearching.value = false;
  }
};

const saveImageAsResource = () => {
  if (imageResult.value) {
    resources.value.push({
      id: Date.now().toString(),
      title: `${title.value}相关图片`,
      url: imageResult.value,
      type: '图片资料'
    });
    imageResult.value = null;
  }
};

const searchContent = async () => {
  if (!title.value.trim()) {
    alert('请先输入知识点标题');
    return;
  }

  isSearching.value = true;
  try {
    const apiKey = 'sk-X7ppAJyerjTVBoTSC5B99261502f494bB534675301A04e4d';
    const apiUrl = 'https://free.v36.cm/v1/chat/completions';
    const prompt = `请简要介绍"${title.value}"这个知识点的相关内容，包括定义、特点、应用等方面，限制在300字以内。回答必须是纯文本。`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          { role: 'system', content: '你是一个知识图谱辅助工具，擅长简洁地解释各个知识点。' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 500
      })
    });

    const data = await response.json();
    if (data?.choices?.[0]?.message) {
      const result = data.choices[0].message.content.trim();
      if (result) {
        content.value = `${title.value}：\n\n${result}`;
      } else {
        alert('AI未生成相关内容');
      }
    } else {
      console.error('AI API response format error:', data);
      alert('AI搜索失败');
    }
  } catch (error) {
    console.error('AI search failed:', error);
    alert('AI搜索失败');
  } finally {
    isSearching.value = false;
  }
};
</script>

<style scoped>
.node-detail-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 450px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  z-index: 50;
  max-height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.panel-header input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  font-size: 14px;
  color: #334155;
  background-color: white;
}

.panel-header input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.panel-actions {
  display: flex;
  gap: 10px;
  margin-left: 15px;
}

.action-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-button {
  background-color: #4CAF50;
  color: white;
}

.action-button:hover {
  background-color: #45a049;
}

.cancel-button {
  background-color: #f44336;
  color: white;
}

.cancel-button:hover {
  background-color: #d32f2f;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #64748b;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 8px;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.panel-content {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.node-content {
  margin-bottom: 20px;
  flex: 3;
}

.node-relations {
  margin-bottom: 20px;
  flex: 1;
}

.node-content h4, .node-relations h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #334155;
  font-weight: 600;
  border-left: 3px solid #3b82f6;
  padding-left: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8fafc;
  padding: 8px 12px;
  border-radius: 4px;
}

.content-editor {
  width: 100%;
  min-height: 250px;
  padding: 16px;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
  color: #334155;
  background-color: white;
  resize: vertical;
  font-family: inherit;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.05);
  margin-bottom: 16px;
}

.content-editor:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.node-relations ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
}

.node-relations li {
  margin-bottom: 8px;
  cursor: pointer;
  color: #3b82f6;
}

.node-relations li:hover {
  color: #2563eb;
  text-decoration: underline;
}

.relation-type {
  font-size: 12px;
  color: #64748b;
  margin-left: 4px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.search-button {
  background-color: #f8fafc;
  color: #334155;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s;
}

.search-button:hover {
  background-color: #f1f5f9;
  border-color: #94a3b8;
}

.search-icon {
  margin-right: 6px;
  font-size: 16px;
}

.search-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 4px;
  margin-top: 12px;
  color: #64748b;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 添加平滑过渡动画 */
@keyframes panel-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.node-detail-panel {
  animation: panel-fade-in 0.2s ease-out;
}

.node-resources {
  margin-top: 20px;
  flex: 1;
}

.node-resources h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #334155;
  font-weight: 600;
  border-left: 3px solid #3b82f6;
  padding-left: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8fafc;
  padding: 8px 12px;
  border-radius: 4px;
}

.node-resources ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
}

.node-resources li {
  margin-bottom: 8px;
  cursor: pointer;
  color: #3b82f6;
}

.node-resources li:hover {
  color: #2563eb;
  text-decoration: underline;
}

.resource-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  gap: 8px;
}

.add-resource-button {
  background-color: #f8fafc;
  color: #334155;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s;
}

.add-resource-button:hover {
  background-color: #f1f5f9;
  border-color: #94a3b8;
}

.add-icon {
  margin-right: 6px;
  font-size: 16px;
}

.resource-link {
  color: #3b82f6;
  text-decoration: none;
}

.resource-link:hover {
  text-decoration: underline;
}

.resource-type {
  font-size: 12px;
  color: #64748b;
  margin-left: 4px;
}

.image-result {
  margin: 15px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f8fafc;
  border-radius: 4px;
  padding: 10px;
  border: 1px solid #e2e8f0;
}

.image-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.save-image-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s;
}

.save-image-button:hover {
  background-color: #45a049;
  transform: translateY(-2px);
}

.resource-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
</style> 