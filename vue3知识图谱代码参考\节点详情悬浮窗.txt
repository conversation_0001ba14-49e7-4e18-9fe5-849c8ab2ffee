<template>
  <div>
    <div ref="myPage" style="height:calc(100vh);">
      <RelationGraph ref="graphRef" :options="graphOptions" @node-click="onNodeClick" @line-click="onLineClick">
        <template #node="{node}">
          <div @mouseover="showNodeTips(node, $event)" @mouseout="hideNodeTips(node, $event)">
            <div class="c-my-rg-node">
              <i style="font-size: 30px;" :class="node.data.myicon" />
            </div>
            <div style="color: forestgreen;font-size: 16px;position: absolute;width: 160px;height:25px;line-height: 25px;margin-top:5px;margin-left:-48px;text-align: center;background-color: rgba(66,187,66,0.2);">
              {{ node.data.myicon }}
            </div>
          </div>
        </template>
      </RelationGraph>
      <div v-if="isShowNodeTipsPanel" :style="{left: nodeMenuPanelPosition.x + 'px', top: nodeMenuPanelPosition.y + 'px' }" style="z-index: 999;padding:10px;background-color: #ffffff;border:#eeeeee solid 1px;box-shadow: 0px 0px 8px #cccccc;position: absolute;">
        <div style="line-height: 25px;padding-left: 10px;color: #888888;font-size: 12px;">Node Name: {{currentNode.text}}</div>
        <div class="c-node-menu-item">id: {{currentNode.text}}</div>
        <div class="c-node-menu-item">icon: {{currentNode.data.myicon}}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import RelationGraph from 'relation-graph-vue3';
import { RelationGraphComponent } from 'relation-graph-vue3';

const myPage = ref();
const graphRef = ref<RelationGraphComponent>();
const isShowNodeTipsPanel = ref(false);
const nodeMenuPanelPosition = ref({ x: 0, y: 0 });
const currentNode = ref({});
const graphOptions = {
    allowSwitchLineShape: true,
    allowSwitchJunctionPoint: true,
    defaultNodeColor: 'rgba(66,187,66,1)',
    defaultJunctionPoint: 'border'
    // You can refer to the parameters in "Graph" for configuration here

};

const showGraph = async() => {
    const __graph_json_data = {
        rootId: '2',
        nodes: [
            // Note: Your custom properties need to be placed in the data tag like this in the node configuration information, otherwise the data will be lost

            { id: '1', text: 'Node-1', data: { myicon: 'el-icon-star-on' }},
            { id: '2', text: 'Node-2', data: { myicon: 'el-icon-setting' }},
            { id: '3', text: 'Node-3', data: { myicon: 'el-icon-setting' }},
            { id: '4', text: 'Node-4', data: { myicon: 'el-icon-star-on' }},
            { id: '6', text: 'Node-6', data: { myicon: 'el-icon-setting' }},
            { id: '7', text: 'Node-7', data: { myicon: 'el-icon-setting' }},
            { id: '8', text: 'Node-8', data: { myicon: 'el-icon-star-on' }},
            { id: '9', text: 'Node-9', data: { myicon: 'el-icon-headset' }},
            { id: '71', text: 'Node-71', data: { myicon: 'el-icon-headset' }},
            { id: '72', text: 'Node-72', data: { myicon: 'el-icon-s-tools' }},
            { id: '73', text: 'Node-73', data: { myicon: 'el-icon-star-on' }},
            { id: '81', text: 'Node-81', data: { myicon: 'el-icon-s-promotion' }},
            { id: '82', text: 'Node-82', data: { myicon: 'el-icon-s-promotion' }},
            { id: '83', text: 'Node-83', data: { myicon: 'el-icon-star-on' }},
            { id: '84', text: 'Node-84', data: { myicon: 'el-icon-s-promotion' }},
            { id: '85', text: 'Node-85', data: { myicon: 'el-icon-sunny' }},
            { id: '91', text: 'Node-91', data: { myicon: 'el-icon-sunny' }},
            { id: '92', text: 'Node-82', data: { myicon: 'el-icon-sunny' }},
            { id: '51', text: 'Node-51', data: { myicon: 'el-icon-sunny' }},
            { id: '52', text: 'Node-52', data: { myicon: 'el-icon-sunny' }},
            { id: '53', text: 'Node-53', data: { myicon: 'el-icon-sunny' }},
            { id: '54', text: 'Node-54', data: { myicon: 'el-icon-sunny' }},
            { id: '55', text: 'Node-55', data: { myicon: 'el-icon-sunny' }},
            { id: '5', text: 'Node-5', data: { myicon: 'el-icon-sunny' }}
        ],
        lines: [
            { from: '7', to: '71', text: 'Investment' },
            { from: '7', to: '72', text: 'Investment' },
            { from: '7', to: '73', text: 'Investment' },
            { from: '8', to: '81', text: 'Investment' },
            { from: '8', to: '82', text: 'Investment' },
            { from: '8', to: '83', text: 'Investment' },
            { from: '8', to: '84', text: 'Investment' },
            { from: '8', to: '85', text: 'Investment' },
            { from: '9', to: '91', text: 'Investment' },
            { from: '9', to: '92', text: 'Investment' },
            { from: '5', to: '51', text: 'Investment1' },
            { from: '5', to: '52', text: 'Investment' },
            { from: '5', to: '53', text: 'Investment3' },
            { from: '5', to: '54', text: 'Investment4' },
            { from: '5', to: '55', text: 'Investment' },
            { from: '1', to: '2', text: 'Investment' },
            { from: '3', to: '1', text: 'Executive' },
            { from: '4', to: '2', text: 'Executive' },
            { from: '6', to: '2', text: 'Executive' },
            { from: '7', to: '2', text: 'Executive' },
            { from: '8', to: '2', text: 'Executive' },
            { from: '9', to: '2', text: 'Executive' },
            { from: '1', to: '5', text: 'Investment' }
        ]
    };
    const graphInstance = graphRef.value.getInstance();
    await graphInstance.setJsonData(__graph_json_data);
    await graphInstance.moveToCenter();
    await graphInstance.zoomToFit();
};

const onNodeClick = (nodeObject, $event) => {
    console.log('onNodeClick:', nodeObject);
};

const onLineClick = (lineObject, linkObject, $event) => {
    console.log('onLineClick:', lineObject);
};

const showNodeTips = (nodeObject, $event) => {
    currentNode.value = nodeObject;
    const _base_position = myPage.value.getBoundingClientRect();
    console.log('showNodeMenus:', $event.clientX, $event.clientY, _base_position);
    isShowNodeTipsPanel.value = true;
    nodeMenuPanelPosition.value.x = $event.clientX - _base_position.x + 10;
    nodeMenuPanelPosition.value.y = $event.clientY - _base_position.y + 10;
};

const hideNodeTips = (nodeObject, $event) => {
    isShowNodeTipsPanel.value = false;
};

onMounted(() => {
    showGraph();
});
</script>

<style lang="scss">

</style>

<style lang="scss" scoped>
.c-my-rg-node {
  height:80px;line-height: 80px;border-radius: 50%;cursor: pointer;
  display: flex;
  place-items: center;
  justify-content: center;
}
.c-node-menu-item{
  line-height: 30px;padding-left: 10px;cursor: pointer;color: #444444;font-size: 14px;border-top:#efefef solid 1px;
}
.c-node-menu-item:hover{
  background-color: rgba(66,187,66,0.2);
}
</style>
