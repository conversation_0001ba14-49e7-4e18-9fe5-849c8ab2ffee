/* 媒体查询 - 针对特定屏幕尺寸的样式调整 */

/* ===== 超小屏幕 (手机, <576px) ===== */
@media (max-width: 575.98px) {
  /* 调整字体大小 */
  h1 { font-size: 1.8rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.3rem; }
  
  /* 减少页面间距，优化小屏布局 */
  .container { 
    padding-left: 0.75rem; 
    padding-right: 0.75rem; 
  }
  
  /* 移动端的卡片样式调整 */
  .card {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }
  
  /* 移动端按钮尺寸调整 */
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  /* 表格在移动设备上优化显示 */
  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  /* 知识图谱在移动端的样式调整 */
  .graph-container {
    height: 70vh;
  }
  
  /* 在移动端隐藏非关键元素 */
  .hidden-xs {
    display: none !important;
  }
  
  /* 移动端信息层次调整 */
  .homepage-section {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md) 0;
  }
  
  /* 调整导航栏在移动端的外观 */
  .navbar {
    padding: 0.5rem;
  }
}

/* ===== 小屏幕 (平板, 576px - 767px) ===== */
@media (min-width: 576px) and (max-width: 767.98px) {
  /* 平板上的特定调整 */
  h1 { font-size: 2rem; }
  
  /* 在平板上调整卡片布局 */
  .card-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }
  
  .card {
    flex: 0 0 calc(50% - var(--spacing-md));
  }
  
  /* 在平板上隐藏的元素 */
  .hidden-sm {
    display: none !important;
  }
}

/* ===== 中等屏幕 (桌面, 768px - 991px) ===== */
@media (min-width: 768px) and (max-width: 991.98px) {
  /* 桌面优化 */
  .content-wrapper {
    padding: var(--spacing-lg);
  }
  
  /* 中等屏幕的侧边栏 */
  .sidebar {
    width: 220px;
  }
  
  /* 知识图谱在中等屏幕上的显示 */
  .graph-container {
    height: 75vh;
  }
}

/* ===== 大屏幕 (大桌面, 992px - 1199px) ===== */
@media (min-width: 992px) and (max-width: 1199.98px) {
  /* 大屏幕特定的布局 */
  .content-main {
    margin-left: var(--sidebar-width);
    padding: var(--spacing-xl);
  }
  
  /* 增加大屏幕上的内容宽度 */
  .content-wrapper {
    max-width: 1000px;
    margin: 0 auto;
  }
}

/* ===== 超大屏幕 (大显示器, >=1200px) ===== */
@media (min-width: 1200px) {
  /* 超大屏幕特定样式 */
  .container {
    max-width: 1140px;
  }
  
  /* 增强大屏幕上的可读性 */
  body {
    font-size: 17px;
  }
  
  h1 { font-size: 2.5rem; }
  h2 { font-size: 2rem; }
  h3 { font-size: 1.75rem; }
  
  /* 大屏幕上的多列布局 */
  .multi-column {
    column-count: 2;
    column-gap: var(--spacing-xl);
  }
  
  /* 大屏幕上的知识图谱 */
  .graph-container {
    height: 80vh;
  }
  
  /* 大屏幕上的卡片网格 */
  .card-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
  }
}

/* ===== 高密度显示器 (Retina屏幕) ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* 针对高密度显示器优化的样式 */
  .icon-hd {
    display: inline-block;
  }
  
  .icon-sd {
    display: none;
  }
}

/* ===== 特定设备方向 ===== */
/* 横屏模式 */
@media (orientation: landscape) and (max-width: 767.98px) {
  /* 手机横屏特定样式 */
  .navbar {
    padding: 0.25rem 0.5rem;
  }
  
  /* 调整知识图谱尺寸 */
  .graph-container {
    height: 85vh;
  }
}

/* 竖屏模式 */
@media (orientation: portrait) and (max-width: 767.98px) {
  /* 手机竖屏特定样式 */
  .graph-container {
    height: 60vh;
  }
  
  /* 竖屏模式下调整导航栏 */
  .navbar {
    padding: 0.5rem;
  }
}

/* ===== 打印样式 ===== */
@media print {
  /* 打印模式下的样式调整 */
  body {
    font-size: 12pt;
    color: #000;
    background: #fff;
  }
  
  /* 打印时隐藏不必要的元素 */
  .navbar,
  .sidebar,
  .footer,
  .graph-controls,
  .theme-switch,
  .btn {
    display: none !important;
  }
  
  /* 打印时展开所有内容 */
  .collapse {
    display: block !important;
    height: auto !important;
  }
  
  /* 打印时调整链接样式 */
  a {
    color: #000;
    text-decoration: underline;
  }
  
  /* 打印时的页面设置 */
  @page {
    margin: 2cm;
  }
}

/* ===== 特殊设备适配 ===== */
/* 超宽屏幕 */
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
  
  /* 在超宽屏幕上增加内容宽度 */
  .content-wrapper {
    max-width: 1200px;
  }
  
  /* 适应超宽屏幕的知识图谱 */
  .graph-container.expanded {
    width: 100%;
    height: 85vh;
  }
}

/* 减小动画 - 用户偏好减少动画 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 深色模式适配 - 系统设置 */
@media (prefers-color-scheme: dark) {
  /* 仅当用户未手动设置主题时应用 */
  :root:not([data-theme]) {
    /* 使用暗色主题变量 */
    --background-color: #0f172a;
    --text-color: #f8fafc;
    /* 其他暗色模式变量... */
  }
}

/* 辅助功能 - 高对比度 */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #0000E0;
    --secondary-color: #000000;
    --text-color: #000000;
    --background-color: #FFFFFF;
    --border-color: #000000;
  }
  
  [data-theme="dark"] {
    --primary-color: #00BBFF;
    --secondary-color: #FFFFFF;
    --text-color: #FFFFFF;
    --background-color: #000000;
    --border-color: #FFFFFF;
  }
  
  a, button, input, select, textarea {
    outline: 2px solid currentColor;
  }
} 