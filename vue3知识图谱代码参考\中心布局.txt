<template>
  <div>
    <div style="height:calc(100vh);">
      <RelationGraph ref="graphRef" :options="graphOptions" @node-click="onNodeClick" @line-click="onLineClick" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, ref, onMounted } from 'vue';
import RelationGraph from 'relation-graph-vue3';
import type { RGJsonData, RGNode, RGLine, RGLink, RGUserEvent, RGOptions, RelationGraphComponent } from 'relation-graph-vue3';

const graphRef = ref<RelationGraphComponent>();
const graphOptions: RGOptions = {
    debug: true,
    defaultNodeBorderWidth: 0,
    defaultNodeColor: 'rgba(238, 178, 94, 1)',
    allowSwitchLineShape: true,
    allowSwitchJunctionPoint: true,
    defaultLineShape: 1,
    layouts: [
        {
            layoutName: 'center'
        }
    ],
    defaultJunctionPoint: 'border'
};


const showGraph = async() => {
    const __graph_json_data: RGJsonData = {
        rootId: '2',
        nodes: [
            { id: '1', text: 'Node-1' },
            { id: '2', text: 'Node-2' },
            { id: '3', text: 'Node-3' },
            { id: '4', text: 'Node-4' },
            { id: '6', text: 'Node-6' },
            { id: '7', text: 'Node-7' },
            { id: '8', text: 'Node-8' },
            { id: '9', text: 'Node-9' },
            { id: '71', text: 'Node-71' },
            { id: '72', text: 'Node-72' },
            { id: '73', text: 'Node-73' },
            { id: '81', text: 'Node-81' },
            { id: '82', text: 'Node-82' },
            { id: '83', text: 'Node-83' },
            { id: '84', text: 'Node-84' },
            { id: '85', text: 'Node-85' },
            { id: '91', text: 'Node-91' },
            { id: '92', text: 'Node-82' },
            { id: '51', text: 'Node-51' },
            { id: '52', text: 'Node-52' },
            { id: '53', text: 'Node-53' },
            { id: '54', text: 'Node-54' },
            { id: '55', text: 'Node-55' },
            { id: '5', text: 'Node-5' }
        ],
        lines: [
            { from: '7', to: '71', text: 'Investment' },
            { from: '7', to: '72', text: 'Investment' },
            { from: '7', to: '73', text: 'Investment' },
            { from: '8', to: '81', text: 'Investment' },
            { from: '8', to: '82', text: 'Investment' },
            { from: '8', to: '83', text: 'Investment' },
            { from: '8', to: '84', text: 'Investment' },
            { from: '8', to: '85', text: 'Investment' },
            { from: '9', to: '91', text: 'Investment' },
            { from: '9', to: '92', text: 'Investment' },
            { from: '5', to: '51', text: 'Investment1' },
            { from: '5', to: '52', text: 'Investment' },
            { from: '5', to: '53', text: 'Investment3' },
            { from: '5', to: '54', text: 'Investment4' },
            { from: '5', to: '55', text: 'Investment' },
            { from: '1', to: '2', text: 'Investment' },
            { from: '3', to: '1', text: 'Executive' },
            { from: '4', to: '2', text: 'Executive' },
            { from: '6', to: '2', text: 'Executive' },
            { from: '7', to: '2', text: 'Executive' },
            { from: '8', to: '2', text: 'Executive' },
            { from: '9', to: '2', text: 'Executive' },
            { from: '1', to: '5', text: 'Investment' }
        ]
    };
    const graphInstance = graphRef.value?.getInstance();
    if (graphInstance) {
        await graphInstance.setJsonData(__graph_json_data);
        await graphInstance.moveToCenter();
        await graphInstance.zoomToFit();
    }
};

const onNodeClick = (nodeObject: RGNode, $event: RGUserEvent) => {
    console.log('onNodeClick:', nodeObject);
};

const onLineClick = (lineObject: RGLine, linkObject: RGLink, $event: RGUserEvent) => {
    console.log('onLineClick:', lineObject);
};

onMounted(() => {
    showGraph();
});
</script>

<style scoped lang="scss">

</style>
