<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="知识图谱网站 - 可视化学习和知识管理平台" />
    <meta name="keywords" content="知识图谱,学习,知识管理,可视化" />
    <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Custom styles -->
    <link rel="stylesheet" href="/src/assets/styles/node-shape.css" />
    <title>知识图谱网站</title>
    <script>
      // 确保页面初始加载时滚动到顶部
      window.onload = function() {
        window.scrollTo(0, 0);
      }
      
      // 在DOMContentLoaded时也滚动到顶部
      document.addEventListener('DOMContentLoaded', function() {
        window.scrollTo(0, 0);
      });
    </script>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
