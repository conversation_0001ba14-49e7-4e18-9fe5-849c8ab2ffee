:root {
  /* 主要颜色 - 统一色彩系统 */
  --primary-color: #2563eb;
  --primary-color-dark: #1d4ed8;
  --primary-color-light: #3b82f6;
  --primary-color-rgb: 37, 99, 235;
  
  --secondary-color: #4b5563;
  --secondary-color-dark: #374151;
  --secondary-color-light: #6b7280;
  --secondary-color-rgb: 75, 85, 99;
  
  --accent-color: #8b5cf6;
  --accent-color-dark: #7c3aed;
  --accent-color-light: #a78bfa;
  --accent-color-rgb: 139, 92, 246;
  
  --success-color: #10b981;
  --success-color-dark: #059669;
  --success-color-light: #34d399;
  --success-color-rgb: 16, 185, 129;
  
  --danger-color: #ef4444;
  --danger-color-dark: #dc2626;
  --danger-color-light: #f87171;
  --danger-color-rgb: 239, 68, 68;
  
  --warning-color: #f59e0b;
  --warning-color-dark: #d97706;
  --warning-color-light: #fbbf24;
  --warning-color-rgb: 245, 158, 11;
  
  --info-color: #0ea5e9;
  --info-color-dark: #0284c7;
  --info-color-light: #38bdf8;
  --info-color-rgb: 14, 165, 233;
  
  /* 背景颜色 */
  --background-color: #ffffff;
  --background-color-rgb: 255, 255, 255;
  --background-light: #f8fafc;
  --background-light-rgb: 248, 250, 252;
  --background-dark: #f1f5f9;
  --background-dark-rgb: 241, 245, 249;
  
  /* 文本颜色 - 提高对比度 */
  --text-color: #0f172a;
  --text-color-rgb: 15, 23, 42;
  --text-light: #334155;
  --text-light-rgb: 51, 65, 85;
  --text-lighter: #64748b;
  --text-lighter-rgb: 100, 116, 139;
  
  /* 边框 */
  --border-color: #cbd5e1;
  --border-color-dark: #94a3b8;
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-width: 1px;
  --border-width-lg: 2px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.04), 0 5px 10px rgba(0, 0, 0, 0.08);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.03);
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-outline: 0 0 0 3px rgba(var(--primary-color-rgb), 0.25);
  
  /* 间距 */
  --spacing-xxs: 2px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  --spacing-xxxl: 64px;
  
  /* 字体大小 */
  --font-size-tiny: 10px;
  --font-size-small: 12px;
  --font-size-medium: 14px;
  --font-size-base: 16px;
  --font-size-large: 18px;
  --font-size-xlarge: 20px;
  --font-size-xxlarge: 24px;
  --font-size-xxxlarge: 32px;
  
  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-very-slow: 0.8s ease;
  
  /* 布局常量 */
  --header-height: 48px;
  --footer-height: 60px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  
  /* 图表颜色 - 简化为统一的调色板 */
  --chart-color-1: #3b82f6;
  --chart-color-2: #f59e0b;
  --chart-color-3: #8b5cf6;
  --chart-color-4: #10b981;
  --chart-color-5: #ef4444;
  --chart-color-6: #0ea5e9;
  --chart-color-7: #ec4899;
  --chart-color-8: #64748b;
  
  /* 动画时间曲线 */
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  /* 渐变 - 统一的渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
  --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-color-dark));
  --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--accent-color-dark));
  --gradient-success: linear-gradient(135deg, var(--success-color), var(--success-color-dark));
  --gradient-danger: linear-gradient(135deg, var(--danger-color), var(--danger-color-dark));
  --gradient-warning: linear-gradient(135deg, var(--warning-color), var(--warning-color-dark));
  --gradient-info: linear-gradient(135deg, var(--info-color), var(--info-color-dark));
  
  /* 特效 */
  --backdrop-blur: blur(10px);
  --backdrop-blur-light: blur(5px);
  --backdrop-blur-heavy: blur(20px);
  
  /* Z-index层级 */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

/* 暗色模式 - 增强对比度和可读性 */
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #0f172a;
    --background-color-rgb: 15, 23, 42;
    --background-light: #1e293b;
    --background-light-rgb: 30, 41, 59;
    --background-dark: #0f172a;
    --background-dark-rgb: 15, 23, 42;
    
    --text-color: #f8fafc;
    --text-color-rgb: 248, 250, 252;
    --text-light: #e2e8f0;
    --text-light-rgb: 226, 232, 240;
    --text-lighter: #cbd5e1;
    --text-lighter-rgb: 203, 213, 225;
    
    --border-color: #334155;
    --border-color-dark: #475569;
    
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.4), 0 5px 10px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.4), 0 10px 20px rgba(0, 0, 0, 0.3);
    
    /* 暗模式下调整主色调提高辨识度 */
    --primary-color: #3b82f6;
    --primary-color-dark: #2563eb;
    --primary-color-light: #60a5fa;
    --primary-color-rgb: 59, 130, 246;
    
    --secondary-color: #64748b;
    --secondary-color-dark: #4b5563;
    --secondary-color-light: #94a3b8;
    
    --accent-color: #a78bfa;
    --accent-color-dark: #8b5cf6;
    --accent-color-light: #c4b5fd;
    
    --success-color: #34d399;
    --success-color-dark: #10b981;
    --success-color-light: #6ee7b7;
    
    --danger-color: #f87171;
    --danger-color-dark: #ef4444;
    --danger-color-light: #fca5a5;
    
    --warning-color: #fbbf24;
    --warning-color-dark: #f59e0b;
    --warning-color-light: #fcd34d;
    
    --info-color: #38bdf8;
    --info-color-dark: #0ea5e9;
    --info-color-light: #7dd3fc;
    
    /* 暗模式下调整图表颜色以提高可见性 */
    --chart-color-1: #60a5fa;
    --chart-color-2: #fcd34d;
    --chart-color-3: #c4b5fd;
    --chart-color-4: #6ee7b7;
    --chart-color-5: #fca5a5;
    --chart-color-6: #7dd3fc;
    --chart-color-7: #f9a8d4;
    --chart-color-8: #cbd5e1;
  }
} 
 