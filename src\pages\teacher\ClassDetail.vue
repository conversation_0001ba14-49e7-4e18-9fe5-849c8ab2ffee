<template>
  <div class="class-detail-page">
    <div class="container py-3 full-width">
      <!-- 页面头部 -->
      <div class="page-header">
        <button class="back-button" @click="goBack">
          <i class="back-icon"></i>
          <span>返回</span>
        </button>
        <h1 class="page-title">班级详情</h1>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载班级详情...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-icon">⚠️</div>
        <h3>加载失败</h3>
        <p>{{ errorMessage }}</p>
        <button class="btn btn-primary" @click="fetchClassDetail">重试</button>
      </div>

      <!-- 班级详情内容 -->
      <div v-else-if="classDetail" class="class-detail-content">
        <!-- 班级基本信息卡片 -->
        <div class="info-card">
          <div class="card-header">
            <h2 class="card-title">{{ classDetail.name || '未命名班级' }}</h2>
            <div class="class-status" :class="getStatusClass(classDetail.status)">
              {{ getStatusText(classDetail.status) }}
            </div>
          </div>
          
          <div class="card-content">
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">学期</span>
                <span class="info-value">{{ classDetail.semester || '未设置' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">教师</span>
                <span class="info-value">{{ classDetail.teacher || classDetail.createBy || '未知' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">学生人数</span>
                <span class="info-value">{{ classDetail.studentCount || 0 }} 人</span>
              </div>
              <div class="info-item">
                <span class="info-label">课程数量</span>
                <span class="info-value">{{ classDetail.courseCount || 0 }} 门</span>
              </div>
              <div class="info-item">
                <span class="info-label">开始日期</span>
                <span class="info-value">{{ formatDate(classDetail.startDate) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">结束日期</span>
                <span class="info-value">{{ formatDate(classDetail.endDate) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">创建时间</span>
                <span class="info-value">{{ formatDateTime(classDetail.createTime) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">最后更新</span>
                <span class="info-value">{{ formatDateTime(classDetail.updateTime) }}</span>
              </div>
            </div>
            
            <div v-if="classDetail.remark" class="class-description">
              <h3>班级描述</h3>
              <p>{{ classDetail.remark }}</p>
            </div>
          </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="stats-card">
          <h3 class="card-title">班级统计</h3>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-icon student-icon">👥</div>
              <div class="stat-content">
                <div class="stat-number">{{ classDetail.studentCount || 0 }}</div>
                <div class="stat-label">学生总数</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon course-icon">📚</div>
              <div class="stat-content">
                <div class="stat-number">{{ classDetail.courseCount || 0 }}</div>
                <div class="stat-label">课程数量</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon progress-icon">📊</div>
              <div class="stat-content">
                <div class="stat-number">{{ Math.round(classDetail.avgProgress || 0) }}%</div>
                <div class="stat-label">平均进度</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon activity-icon">⚡</div>
              <div class="stat-content">
                <div class="stat-number">{{ classDetail.activityCount || 0 }}</div>
                <div class="stat-label">活动数量</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <button class="btn btn-primary" @click="manageStudents">
            <i class="icon-users"></i>
            学生管理
          </button>
          <button class="btn btn-secondary" @click="manageCourses">
            <i class="icon-book"></i>
            课程管理
          </button>
          <button class="btn btn-success" @click="viewAnalytics">
            <i class="icon-chart"></i>
            学习分析
          </button>
          <button class="btn btn-warning" @click="editClass">
            <i class="icon-edit"></i>
            编辑班级
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getClassById } from '@/api/class';

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const error = ref(false);
const errorMessage = ref('');
const classDetail = ref(null);

// 获取班级ID
const classId = route.params.id;

// 获取班级详情
const fetchClassDetail = async () => {
  if (!classId) {
    error.value = true;
    errorMessage.value = '班级ID不能为空';
    loading.value = false;
    return;
  }

  try {
    loading.value = true;
    error.value = false;
    
    console.log('正在获取班级详情，ID:', classId);
    const response = await getClassById(classId);
    
    if (response && response.success && response.data) {
      classDetail.value = response.data;
      console.log('班级详情获取成功:', classDetail.value);
    } else {
      throw new Error(response?.msg || '获取班级详情失败');
    }
  } catch (err) {
    console.error('获取班级详情失败:', err);
    error.value = true;
    errorMessage.value = err.message || '获取班级详情失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未设置';
  try {
    return new Date(dateString).toLocaleDateString('zh-CN');
  } catch {
    return dateString;
  }
};

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '未设置';
  try {
    return new Date(dateString).toLocaleString('zh-CN');
  } catch {
    return dateString;
  }
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'active': return 'status-active';
    case 'inactive': return 'status-inactive';
    case 'completed': return 'status-completed';
    default: return 'status-default';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'active': return '进行中';
    case 'inactive': return '未开始';
    case 'completed': return '已结束';
    default: return '未知状态';
  }
};

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 学生管理
const manageStudents = () => {
  console.log('学生管理');
  // TODO: 跳转到学生管理页面
  alert('学生管理功能正在开发中');
};

// 课程管理
const manageCourses = () => {
  console.log('课程管理');
  // TODO: 跳转到课程管理页面
  alert('课程管理功能正在开发中');
};

// 查看分析
const viewAnalytics = () => {
  console.log('查看学习分析');
  // TODO: 跳转到学习分析页面
  alert('学习分析功能正在开发中');
};

// 编辑班级
const editClass = () => {
  console.log('编辑班级');
  // TODO: 跳转到编辑班级页面或显示编辑对话框
  alert('编辑班级功能正在开发中');
};

// 组件挂载时获取班级详情
onMounted(() => {
  fetchClassDetail();
});
</script>

<style scoped>
.class-detail-page {
  min-height: 100vh;
  background-color: var(--background-color-secondary, #f8fafc);
}

.full-width {
  max-width: 100% !important;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-top: 0.5rem;
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: var(--primary-color, #007AFF);
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  margin-right: 1rem;
}

.back-button:hover {
  background-color: rgba(0, 122, 255, 0.1);
}

.back-icon {
  position: relative;
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
}

.back-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 10px;
  height: 10px;
  border-left: 2px solid currentColor;
  border-bottom: 2px solid currentColor;
  transform: translateY(-50%) rotate(45deg);
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-color-primary, #1a202c);
  margin: 0;
}

/* 加载和错误状态样式 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color, #007AFF);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container .error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-container h3 {
  color: var(--text-color-primary, #1a202c);
  margin-bottom: 0.5rem;
}

.error-container p {
  color: var(--text-color-secondary, #64748b);
  margin-bottom: 1.5rem;
}

/* 班级详情内容样式 */
.class-detail-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 信息卡片样式 */
.info-card, .stats-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.class-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-active {
  background-color: rgba(34, 197, 94, 0.2);
  color: #16a34a;
}

.status-inactive {
  background-color: rgba(156, 163, 175, 0.2);
  color: #6b7280;
}

.status-completed {
  background-color: rgba(59, 130, 246, 0.2);
  color: #2563eb;
}

.status-default {
  background-color: rgba(156, 163, 175, 0.2);
  color: #6b7280;
}

.card-content {
  padding: 1.5rem;
}

/* 信息网格样式 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid var(--primary-color, #007AFF);
}

.info-label {
  font-weight: 500;
  color: var(--text-color-secondary, #64748b);
}

.info-value {
  font-weight: 600;
  color: var(--text-color-primary, #1a202c);
}

/* 班级描述样式 */
.class-description {
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
}

.class-description h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color-primary, #1a202c);
  margin-bottom: 0.75rem;
}

.class-description p {
  color: var(--text-color-secondary, #64748b);
  line-height: 1.6;
}

/* 统计卡片样式 */
.stats-card .card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color-primary, #1a202c);
  margin: 0 0 1.5rem 0;
  padding: 1.5rem 1.5rem 0 1.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
}

.student-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.course-icon {
  background: linear-gradient(135deg, #10b981 0%, #047857 100%);
}

.progress-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.activity-icon {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color-primary, #1a202c);
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary, #64748b);
  margin-top: 0.25rem;
}

/* 操作按钮样式 */
.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  gap: 0.5rem;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #047857 100%);
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

/* 图标样式 */
.icon-users::before { content: "👥"; }
.icon-book::before { content: "📚"; }
.icon-chart::before { content: "📊"; }
.icon-edit::before { content: "✏️"; }

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .full-width {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .stat-item {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
}
</style>
