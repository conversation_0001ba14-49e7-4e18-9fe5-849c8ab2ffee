/* Glassmorphism UI 风格基础样式 - Context7 增强版 */

:root {
  --glass-bg-primary: rgba(255, 255, 255, 0.15);
  --glass-bg-secondary: rgba(255, 255, 255, 0.1);
  --glass-bg-tertiary: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
  --glass-shadow-hover: 0 12px 40px rgba(31, 38, 135, 0.25);
  --glass-blur: 16px;
  --glass-blur-strong: 24px;
  --glass-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark {
  --glass-bg-primary: rgba(0, 0, 0, 0.25);
  --glass-bg-secondary: rgba(0, 0, 0, 0.15);
  --glass-bg-tertiary: rgba(0, 0, 0, 0.1);
  --glass-border: rgba(255, 255, 255, 0.1);
}

/* 基础玻璃态样式 */
.glass {
  background: var(--glass-bg-primary);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: var(--glass-transition);
}

.glass-strong {
  background: var(--glass-bg-secondary);
  backdrop-filter: blur(var(--glass-blur-strong));
  -webkit-backdrop-filter: blur(var(--glass-blur-strong));
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

/* 现代卡片样式 */
.glass-card {
  background: var(--glass-bg-primary);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border-radius: 20px;
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  transition: var(--glass-transition);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  z-index: 1;
}

.glass-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--glass-shadow-hover);
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.2);
}

/* 现代按钮样式 */
.glass-button {
  background: var(--glass-bg-primary);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  padding: 12px 24px;
  font-weight: 600;
  transition: var(--glass-transition);
  color: rgba(0, 0, 0, 0.8);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.glass-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.glass-button:hover::before {
  width: 200px;
  height: 200px;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow-hover);
}

/* 现代输入框样式 */
.glass-input {
  background: var(--glass-bg-secondary);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  padding: 12px 16px;
  transition: var(--glass-transition);
  color: rgba(0, 0, 0, 0.8);
  position: relative;
}

.glass-input:focus {
  background: var(--glass-bg-primary);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2), 0 0 0 3px rgba(255, 255, 255, 0.1);
  outline: none;
}

/* 特殊背景效果 - Context7 风格 */
.glass-morphic-bg {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.05) 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
}

.glass-morphic-bg::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, 
    transparent 0deg, 
    rgba(255, 255, 255, 0.05) 90deg, 
    transparent 180deg, 
    rgba(255, 255, 255, 0.03) 270deg, 
    transparent 360deg);
  animation: rotate 20s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Header 专用样式 */
.glass-header {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
}

/* Footer 专用样式 */
.glass-footer {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 -4px 24px rgba(0, 0, 0, 0.08);
}

/* 彩色光斑效果 - Context7 增强版 */
.glass-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.4;
  z-index: -1;
  animation: float 25s infinite ease-in-out;
}

.glass-orb-1 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(125, 211, 252, 0.6) 0%, rgba(125, 211, 252, 0.2) 70%, transparent 100%);
  top: 10%;
  left: 5%;
  animation-delay: 0s;
}

.glass-orb-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(167, 139, 250, 0.5) 0%, rgba(167, 139, 250, 0.2) 70%, transparent 100%);
  bottom: 5%;
  right: 10%;
  animation-delay: -8s;
}

.glass-orb-3 {
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(251, 146, 60, 0.4) 0%, rgba(251, 146, 60, 0.15) 70%, transparent 100%);
  top: 50%;
  right: 20%;
  animation-delay: -15s;
}

.glass-orb-4 {
  width: 350px;
  height: 350px;
  background: radial-gradient(circle, rgba(34, 197, 94, 0.4) 0%, rgba(34, 197, 94, 0.15) 70%, transparent 100%);
  top: 30%;
  left: 60%;
  animation-delay: -20s;
}

@keyframes float {
  0%, 100% { 
    transform: translate(0px, 0px) scale(1); 
    opacity: 0.4;
  }
  25% { 
    transform: translate(30px, -60px) scale(1.1); 
    opacity: 0.6;
  }
  50% { 
    transform: translate(-40px, 40px) scale(0.9); 
    opacity: 0.3;
  }
  75% { 
    transform: translate(20px, -30px) scale(1.05); 
    opacity: 0.5;
  }
}

/* 渐变网格背景 */
.glass-grid-bg {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .glass-card {
    border-radius: 16px;
  }
  
  .glass-button {
    border-radius: 10px;
    padding: 10px 20px;
  }
  
  .glass-input {
    border-radius: 10px;
    padding: 10px 14px;
  }
  
  .glass-orb {
    filter: blur(40px);
  }
  
  .glass-orb-1, .glass-orb-2 {
    width: 200px;
    height: 200px;
  }
  
  .glass-orb-3, .glass-orb-4 {
    width: 150px;
    height: 150px;
  }
}

/* 性能优化 */
@media (prefers-reduced-motion: reduce) {
  .glass-orb,
  .glass-morphic-bg::before {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .glass {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(0, 0, 0, 0.5);
  }
  
  .glass-card {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.3);
  }
} 