<template>
  <DefaultLayout>
    <div class="glass-morphic-bg main-homepage-container">
      <!-- 彩色光斑背景效果 -->
      <div class="glass-orb glass-orb-1"></div>
      <div class="glass-orb glass-orb-2"></div>
      <div class="glass-orb glass-orb-3"></div>
      <div class="glass-orb glass-orb-4"></div>

      <!-- 轮播图区域 -->
      <section class="hero-section">
        <div class="container">
          <Carousel />
        </div>
      </section>

      <!-- 知识图谱体验区域 -->
      <section class="knowledge-graph-experience">
        <InteractiveDemo />
      </section>

      <!-- 平台特色展示区域 -->
      <section class="platform-features">
        <div class="container">
          <h2 class="section-title">平台特色功能</h2>
          <p class="section-description">
            HiProf 智能教育平台为教师和学生提供全方位的教学与学习支持
          </p>

          <div class="features-grid">
            <div class="feature-card glass-card">
              <div class="feature-icon">🔍</div>
              <h3 class="feature-title">知识图谱可视化</h3>
              <p class="feature-description">
                通过交互式图谱直观展示知识点关联，构建完整知识体系
              </p>
            </div>

            <div class="feature-card glass-card">
              <div class="feature-icon">🤖</div>
              <h3 class="feature-title">AI智能助手</h3>
              <p class="feature-description">
                基于先进AI技术，提供个性化问答和学习建议
              </p>
            </div>

            <div class="feature-card glass-card">
              <div class="feature-icon">📚</div>
              <h3 class="feature-title">智能备课工具</h3>
              <p class="feature-description">
                为教师提供教案生成、PPT制作等智能备课功能
              </p>
            </div>

            <div class="feature-card glass-card">
              <div class="feature-icon">🎓</div>
              <h3 class="feature-title">个性化学习</h3>
              <p class="feature-description">
                为学生提供个性化学习路径和进度跟踪
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  </DefaultLayout>
</template>

<script setup>
import { onMounted, nextTick } from 'vue';
import DefaultLayout from '@/layouts/DefaultLayout.vue';
import Carousel from '@/components/home/<USER>';
import InteractiveDemo from '@/components/home/<USER>';

// 处理页面加载
onMounted(() => {
  nextTick(() => {
    window.scrollTo({ top: 0, behavior: 'instant' });
  });
});
</script>

<style scoped>
@import '../assets/styles/glassmorphism.css';

.main-homepage-container {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(224, 242, 254, 0.8) 0%, 
    rgba(240, 253, 250, 0.8) 25%,
    rgba(254, 249, 195, 0.8) 50%,
    rgba(250, 232, 255, 0.8) 75%,
    rgba(239, 246, 255, 0.8) 100%);
  position: relative;
  backdrop-filter: blur(var(--glass-blur, 16px));
  -webkit-backdrop-filter: blur(var(--glass-blur, 16px));
}

/* 添加微妙的网格背景 */
.main-homepage-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: 1;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 2;
}

/* 轮播图区域 */
.hero-section {
  padding: var(--spacing-xl) 0;
}

/* 知识图谱体验区域 */
.knowledge-graph-experience {
  padding: var(--spacing-xl) 0;
}

/* 平台特色区域 */
.platform-features {
  padding: var(--spacing-xl) 0 var(--spacing-xxl);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: var(--spacing-md);
  color: rgba(0, 0, 0, 0.85);
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.5);
}

.section-description {
  font-size: 1.125rem;
  color: rgba(0, 0, 0, 0.7);
  text-align: center;
  max-width: 600px;
  margin: 0 auto var(--spacing-xl);
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-xl);
  text-align: center;
  transition: var(--glass-transition);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--glass-shadow-hover);
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.25);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: rgba(0, 0, 0, 0.9);
}

.feature-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(0, 0, 0, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .feature-card {
    padding: var(--spacing-lg);
  }

  .container {
    padding: 0 var(--spacing-md);
  }

  .section-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 1.75rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .feature-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
  }

  .feature-title {
    font-size: 1.25rem;
  }
}
</style>
