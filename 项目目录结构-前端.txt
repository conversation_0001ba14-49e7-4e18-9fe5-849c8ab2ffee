# HiProf教师备课助手前端项目目录结构

```
HiProf/
├── dist/                       # 构建输出目录
│   ├── assets/                 # 构建后的静态资源
│   ├── favicon.svg             # 网站图标
│   └── index.html              # 构建后的HTML文件
├── node_modules/               # 依赖包目录
├── src/                        # 源代码目录
│   ├── api/                    # API请求模块
│   │   ├── axios.js            # Axios配置和请求拦截器
│   │   ├── auth.js             # 认证相关API
│   │   ├── courses.js          # 课程相关API
│   │   ├── coze.ts             # Coze AI助手API (TypeScript)
│   │   ├── graph.js            # 知识图谱相关API
│   │   ├── home.js             # 首页相关API
│   │   ├── lessonPlan.js       # 教案相关API
│   │   └── outline.js          # 大纲相关API
│   ├── assets/                 # 资源文件
│   │   ├── css/                # CSS样式文件
│   │   │   ├── dark-mode.css   # 暗黑模式样式
│   │   │   ├── global.css      # 全局样式
│   │   │   ├── media-queries.css # 媒体查询样式
│   │   │   ├── responsive.css  # 响应式样式
│   │   │   ├── theme.css       # 主题样式
│   │   │   └── variables.css   # CSS变量
│   │   ├── images/             # 图片资源
│   │   │   ├── icons/          # 图标文件
│   │   │   ├── efficient-learning.svg # 高效学习图标
│   │   │   ├── efficient-review.svg   # 高效复习图标
│   │   │   ├── energy-management.svg  # 能量管理图标
│   │   │   └── logo.svg        # 项目Logo
│   │   └── styles/             # 样式文件
│   │       ├── glassmorphism.css # 玻璃态效果样式
│   │       ├── global.css      # 全局样式
│   │       ├── graph-overrides.css # 图谱样式覆盖
│   │       ├── node-shape.css  # 节点形状样式
│   │       ├── theme.css       # 主题样式
│   │       └── variables.css   # CSS变量
│   ├── components/             # 可复用组件
│   │   ├── chat/               # 聊天相关组件 (预留)
│   │   ├── common/             # 通用组件
│   │   │   ├── AppFooter.vue   # 应用底部组件
│   │   │   ├── AppHeader.vue   # 应用头部组件
│   │   │   ├── BaseButton.vue  # 基础按钮组件
│   │   │   ├── BaseCard.vue    # 基础卡片组件
│   │   │   ├── BaseInput.vue   # 基础输入框组件
│   │   │   ├── BaseModal.vue   # 基础模态框组件
│   │   │   ├── ErrorBoundary.vue # 错误边界组件
│   │   │   ├── LoadingSpinner.vue # 加载动画组件
│   │   │   └── NotificationToast.vue # 通知提示组件
│   │   ├── graph/              # 图谱相关组件
│   │   │   ├── GraphList.vue   # 图谱列表组件
│   │   │   ├── NodeContextMenu.vue # 节点右键菜单
│   │   │   ├── NodeDetailPanel.vue # 节点详情面板
│   │   │   └── NodeEditDialog.vue  # 节点编辑对话框
│   │   ├── home/               # 首页相关组件
│   │   │   ├── AnnouncementBar.vue # 公告栏组件
│   │   │   ├── Carousel.vue    # 轮播图组件
│   │   │   ├── InteractiveDemo.vue # 交互演示组件
│   │   │   ├── PopularGraphCard.vue # 热门图谱卡片
│   │   │   └── SystemOverview.vue  # 系统概览组件
│   │   ├── my/                 # 个人中心相关组件
│   │   │   ├── ChangePasswordDialog.vue # 修改密码对话框
│   │   │   ├── EditProfileDialog.vue    # 编辑资料对话框
│   │   │   ├── MyDashboard.vue # 个人仪表板
│   │   │   ├── MyFavorites.vue # 我的收藏
│   │   │   ├── MyGraphList.vue # 我的图谱列表
│   │   │   ├── MyGraphs.vue    # 我的图谱
│   │   │   ├── MyHistory.vue   # 我的历史
│   │   │   ├── MyOutlineList.vue # 我的大纲列表
│   │   │   ├── MyProfile.vue   # 我的资料
│   │   │   └── MySettings.vue  # 我的设置
│   │   └── outline/            # 大纲相关组件
│   │       ├── CourseSelector.vue # 课程选择器
│   │       ├── OutlineEditor.vue  # 大纲编辑器
│   │       ├── OutlineImport.vue  # 大纲导入组件
│   │       ├── OutlineList.vue    # 大纲列表
│   │       ├── OutlineNode.vue    # 大纲节点
│   │       ├── OutlineNodeItem.vue # 大纲节点项
│   │       ├── OutlineToolbar.vue  # 大纲工具栏
│   │       └── OutlineTree.vue     # 大纲树组件
│   ├── composables/            # 组合式API函数
│   │   └── useChatNonStream.ts # 非流式聊天钩子 (TypeScript)
│   ├── layouts/                # 布局组件
│   │   ├── DefaultLayout.vue   # 默认布局
│   │   ├── StudentLayout.vue   # 学生布局
│   │   └── TeacherLayout.vue   # 教师布局
│   ├── pages/                  # 页面组件
│   │   ├── student/            # 学生页面
│   │   │   ├── StudentCoursesPage.vue      # 学生课程页面
│   │   │   ├── StudentDashboardPage.vue    # 学生仪表板页面
│   │   │   ├── StudentKnowledgeGraphPage.vue # 学生知识图谱页面
│   │   │   ├── StudentMaterialsPage.vue    # 学生资料页面
│   │   │   └── StudentProgressPage.vue     # 学生进度页面
│   │   ├── teacher/            # 教师页面
│   │   │   ├── LessonCreatePage.vue    # 课程创建页面
│   │   │   ├── LessonEditPage.vue      # 课程编辑页面
│   │   │   ├── LessonGeneratorPage.vue # 课程生成页面
│   │   │   ├── LessonListPage.vue      # 课程列表页面
│   │   │   ├── MaterialsPage.vue       # 教材页面
│   │   │   └── PPTGeneratorPage.vue    # PPT生成页面
│   │   ├── AIAssistantPage.vue     # AI助手页面
│   │   ├── ComingSoonPage.vue      # 即将推出页面
│   │   ├── ForgotPasswordPage.vue  # 忘记密码页面
│   │   ├── GraphPage.vue           # 知识图谱页面
│   │   ├── KnowledgeGraphHomePage.vue # 知识图谱首页
│   │   ├── LoginPage.vue           # 登录页面
│   │   ├── MainHomePage.vue        # 主首页
│   │   ├── MyPage.vue              # 我的页面
│   │   ├── OutlinePage.vue         # 大纲页面
│   │   ├── RegisterPage.vue        # 注册页面
│   │   └── ResetPasswordPage.vue   # 重置密码页面
│   ├── router/                 # 路由配置
│   │   ├── index.js            # 路由主文件
│   │   └── routes.js           # 路由定义
│   ├── utils/                  # 工具函数
│   │   ├── auth.js             # 认证工具函数
│   │   ├── lessonParser.js     # 课程解析工具
│   │   ├── notification.js     # 通知工具
│   │   ├── responsive.js       # 响应式工具
│   │   └── wordGenerator.js    # Word文档生成工具
│   ├── App.vue                 # 应用根组件
│   ├── index.html              # HTML模板文件
│   └── main.js                 # 应用入口文件
├── vue3知识图谱代码参考/        # Vue3知识图谱参考代码
│   ├── 中心布局.txt            # 中心布局参考
│   ├── 双击编辑节点文字/       # 双击编辑节点参考
│   ├── 层次布局.txt            # 层次布局参考
│   ├── 点击选中.txt            # 点击选中参考
│   ├── 节点右键菜单.txt        # 节点右键菜单参考
│   ├── 节点和子节点.txt        # 节点和子节点参考
│   ├── 节点大小.txt            # 节点大小参考
│   ├── 节点详情悬浮窗.txt      # 节点详情悬浮窗参考
│   └── 节点详细悬停，全局.txt  # 节点详细悬停参考
├── debug-parser.html           # 调试解析器HTML文件
├── index.html                  # 项目入口HTML文件
├── main.js                     # 主入口文件
├── main.ts                     # 主入口文件 (TypeScript)
├── package.json                # 项目依赖配置
├── package-lock.json           # 依赖锁定文件
├── vite.config.ts              # Vite配置文件 (TypeScript)
├── LESSON_EDIT_FEATURE.md      # 课程编辑功能说明
├── README.md                   # 项目说明文档
├── README1.md                  # 项目说明文档1
└── 项目目录结构-前端.txt       # 前端项目目录结构说明
```

## 项目技术栈

### 核心框架
- **Vue 3.2.47**: 采用组合式API，提供更好的TypeScript支持和性能优化
- **Vite 4.1.2**: 现代化构建工具，支持快速热重载和优化的生产构建
- **Vue Router 4.2.5**: Vue 3官方路由管理器，支持动态路由和路由守卫

### 状态管理
- **Pinia 2.1.7**: Vue 3推荐的状态管理库，比Vuex更轻量且支持TypeScript

### UI组件库
- **Element Plus 2.10.3**: 基于Vue 3的企业级UI组件库，提供丰富的组件和主题定制

### 图谱可视化
- **relation-graph-vue3 2.2.10**: 专为Vue 3设计的关系图谱组件，支持复杂的节点关系展示

### HTTP客户端
- **Axios 1.6.2**: 基于Promise的HTTP客户端，支持请求/响应拦截器

### AI集成
- **@coze/api 1.3.5**: Coze AI助手API集成，支持智能对话和内容生成

### 文档处理
- **docx 9.5.1**: JavaScript库，用于生成和处理Word文档
- **js-cookie 3.0.5**: 轻量级的JavaScript Cookie操作库

### 动画效果
- **motion-v 1.3.1**: Vue 3动画库，提供流畅的过渡效果

### 开发工具
- **@vitejs/plugin-vue 4.0.0**: Vite的Vue插件，支持单文件组件
- **TypeScript支持**: 部分文件使用TypeScript，提供更好的类型安全

### MCP工具链 (开发辅助)
- **mcp-sequential-thinking**: 序列思维工具
- **mcp-cursor-companion**: 光标伴侣工具
- **task-master-ai**: AI任务管理工具
- **mcp-shrimp-task-manager**: 任务管理器

## 模块功能说明

### API模块 (src/api/)
- **axios.js**: HTTP请求配置，包含拦截器和错误处理
- **auth.js**: 用户认证相关API（登录、注册、密码重置）
- **courses.js**: 课程管理相关API
- **coze.ts**: Coze AI助手集成API
- **graph.js**: 知识图谱数据操作API
- **home.js**: 首页数据获取API
- **lessonPlan.js**: 教案管理API
- **outline.js**: 大纲管理API

### 组件模块 (src/components/)
- **common/**: 通用基础组件，如按钮、输入框、模态框等
- **graph/**: 知识图谱相关组件，包含节点操作和图谱展示
- **home/**: 首页展示组件，包含轮播图、公告栏等
- **my/**: 个人中心组件，包含个人信息管理和设置
- **outline/**: 大纲编辑和管理组件

### 页面模块 (src/pages/)
- **student/**: 学生角色专用页面，包含学习进度、课程等
- **teacher/**: 教师角色专用页面，包含课程创建、教案生成等
- **通用页面**: 登录、注册、首页等公共页面

### 工具模块 (src/utils/)
- **auth.js**: 认证状态管理和Token处理
- **lessonParser.js**: 课程内容解析工具
- **notification.js**: 消息通知工具
- **responsive.js**: 响应式布局工具
- **wordGenerator.js**: Word文档生成工具

## 安全措施

1. **前端安全配置**
   - 配置Content Security Policy (CSP)以防止XSS攻击
   - 实现请求拦截器以处理JWT认证
   - 所有用户输入经过验证和转义
   - 敏感数据不存储在LocalStorage中
   - 针对SQL注入的前端防御（输入验证和过滤）

2. **权限控制**
   - 基于用户角色的视图渲染控制
   - 路由守卫确保特定页面需要认证
   - 教师/学生角色页面分离，确保权限隔离
   - 组件级别的权限控制

## 性能优化策略

1. **构建优化**
   - 使用Vite进行快速构建和热模块替换
   - 代码分割和懒加载减少初始加载时间
   - 静态资源压缩和缓存策略

2. **运行时优化**
   - 组件懒加载和动态导入
   - 虚拟列表处理大数据集
   - 防抖和节流优化用户交互
   - 图片懒加载和资源预加载

3. **数据优化**
   - API响应缓存减少重复请求
   - 分页加载大型数据集
   - 数据预取提升用户体验
   - 状态管理优化避免不必要的重渲染

## 项目特色功能

### 1. 智能教案生成
- **AI驱动**: 集成Coze AI助手，支持智能教案内容生成
- **多格式输出**: 支持生成Word文档格式的教案
- **模板化**: 提供多种教案模板，适应不同学科需求
- **个性化定制**: 根据教师偏好和学科特点定制生成内容

### 2. 知识图谱可视化
- **交互式图谱**: 基于relation-graph-vue3的知识点关系展示
- **多种布局**: 支持中心布局、层次布局等多种图谱展示方式
- **节点操作**: 支持节点的创建、编辑、删除和关系连接
- **右键菜单**: 丰富的节点操作菜单，提升用户体验
- **悬浮详情**: 节点悬停显示详细信息，支持全局悬浮窗

### 3. 大纲管理系统
- **树形结构**: 支持多层级的课程大纲组织
- **拖拽排序**: 直观的拖拽操作调整大纲结构
- **导入导出**: 支持多种格式的大纲导入和导出
- **协作编辑**: 支持多人协作编辑大纲内容

### 4. 角色权限管理
- **双角色设计**: 区分教师和学生两种用户角色
- **专用界面**: 为不同角色提供专门的操作界面
- **权限隔离**: 确保不同角色只能访问相应的功能模块
- **个性化布局**: 根据角色提供不同的页面布局和导航

### 5. 响应式设计
- **多设备适配**: 支持桌面端、平板和移动端的响应式布局
- **暗黑模式**: 提供暗黑主题，保护用户视力
- **主题定制**: 支持多种主题色彩和样式定制
- **无障碍支持**: 遵循Web无障碍设计标准

### 6. 文档处理能力
- **Word生成**: 支持生成标准格式的Word教案文档
- **模板系统**: 内置多种文档模板，支持自定义模板
- **格式保持**: 确保生成文档的格式规范和美观
- **批量处理**: 支持批量生成和导出文档

## 开发环境配置

### 环境要求
- **Node.js**: 版本 >= 16.0.0
- **npm**: 版本 >= 8.0.0 或 yarn >= 1.22.0
- **现代浏览器**: 支持ES2020+的浏览器

### 开发命令
```bash
# 安装依赖（使用国内镜像）
npm install --registry=https://registry.npmmirror.com

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 配置说明
- **vite.config.ts**: Vite构建配置，包含路径别名和插件配置
- **package.json**: 项目依赖和脚本配置
- **TypeScript支持**: 部分文件支持TypeScript，提供更好的开发体验

## 部署说明

### 构建输出
- 构建后的文件位于 `dist/` 目录
- 包含优化后的静态资源和HTML文件
- 支持CDN部署和静态文件服务器部署

### 环境变量
- 支持多环境配置（开发、测试、生产）
- API接口地址可通过环境变量配置
- 支持功能开关的环境变量控制