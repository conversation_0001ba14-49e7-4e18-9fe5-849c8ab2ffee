<template>
  <div>
    <div style="height:calc(100vh);">
      <RelationGraph
        ref="$graphRef"
        :options="graphOptions">
        <template #node="{node}">
          <MyEditableNodeSlot :node="node" :graph-instance="graphInstance" :enable-editing-mode="enableEditingMode" @on-node-text-change="onNodeTextChange" />
        </template>
        <template #graph-plug>
          <MyDemoPanel width="300px" left="10px">
            <div class="c-content">
              Double click on node text to start editing.
            </div>
            <div>
              <div class="c-button" @click="toggleEditingMode">{{ enableEditingMode?'Node Text Editing - Enabled':'Node Text Editing - Disabled' }}</div>
            </div>
          </MyDemoPanel>
        </template>
      </RelationGraph>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ref, onMounted, computed} from 'vue';
import MyDemoPanel from './RGDemoComponents/MyDemoPanel.vue';
import MyEditableNodeSlot from './RGFlowEditor/MyEditableNodeSlot.vue';
import RelationGraph from 'relation-graph-vue3';
import type { RelationGraphComponent, RGJsonData, RGOptions, RGNode, RGUserEvent } from 'relation-graph-vue3';
import {RelationGraphInstance} from "relation-graph-vue3";
const enableEditingMode = ref(true);
const graphOptions: RGOptions = {
    allowShowMiniToolBar: false,
    defaultLineColor: '#43a2f1',
    defaultLineWidth: 2,
    layout: {
        layoutName: 'fixed'
    }
};

const $graphRef = ref<RelationGraphComponent>(null);
const graphInstance = computed<RelationGraphInstance>(() => $graphRef.value?.getInstance());
const showGraph = () => {
    const docJsonString = '{"rootId":"a","nodes":[{"id":"a","text":"Border color","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"","className":"","borderColor":"yellow","opacity":1,"fixed":false,"x":-48,"y":-48,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}},{"id":"a1","text":"No border","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"","className":"","borderWidth":-1,"color":"#ff8c00","opacity":1,"fixed":false,"x":271.20000000000005,"y":-48.00000000000002,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}},{"id":"a2","text":"Plain","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"","className":"","borderWidth":3,"borderColor":"#ff8c00","fontColor":"#ff8c00","color":"transparent","opacity":1,"fixed":false,"x":71.15255161093671,"y":243.90234677760276,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}},{"id":"a1-1","text":"Text Node","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"","className":"","opacity":1,"fixed":false,"x":550.1012716381912,"y":-208.26075272748085,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}},{"id":"a1-4","text":"XXX","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"","className":"","nodeShape":0,"opacity":1,"fixed":false,"x":550.1012716381912,"y":112.26075272748065,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}},{"id":"b","text":"Font color","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"","className":"","fontColor":"#ffd700","color":"#43a2f1","opacity":1,"fixed":false,"x":74.15255161093667,"y":-342.90234677760276,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}},{"id":"d","text":"Node Size","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"","className":"","borderWidth":5,"borderColor":"#ffd700","fontColor":"#ffffff","color":"#ff8c00","opacity":1,"fixed":false,"width":150,"height":150,"x":-382.9023467776028,"y":34.152551610936584,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}},{"id":"e","text":"Rectangular node","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"","className":"","nodeShape":1,"opacity":1,"fixed":false,"x":-174.0822391109366,"y":-322.9023467776028,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}},{"id":"f","text":"Rectangular","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"","className":"","nodeShape":1,"borderWidth":1,"opacity":1,"fixed":false,"width":300,"height":60,"x":-731.0662065309889,"y":197.95758132046345,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}},{"id":"f1","text":"Fixed","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"","className":"","opacity":1,"fixed":true,"x":60,"y":60,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}},{"id":"g","text":"Css Flash","isShow":true,"isHide":false,"expanded":true,"selected":false,"styleClass":"my-node-flash-style","className":"","opacity":1,"fixed":false,"x":-342.90234677760276,"y":-170.15255161093674,"offset_x":0,"offset_y":0,"disableDrag":false,"singleNode":false,"data":{}}],"lines":[{"from":"a","to":"b","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}},{"from":"a","to":"a1","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}},{"from":"a1","to":"a1-1","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}},{"from":"a","to":"a2","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}},{"from":"a1","to":"a1-4","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}},{"from":"a","to":"f1","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}},{"from":"a","to":"d","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}},{"from":"d","to":"f","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}},{"from":"a","to":"g","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}},{"from":"a","to":"e","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}},{"from":"b","to":"e","text":"","opacity":1,"isHide":false,"animation":0,"dashType":0,"disableDefaultClickEffect":false,"showStartArrow":false,"showEndArrow":true,"forDisplayOnly":false,"hidden":false,"data":{}}]}';
    const docFromDB: RGJsonData = JSON.parse(docJsonString);

    $graphRef.value.setJsonData(docFromDB, (graphInstance) => {
    // These are the codes to be executed after the graph is initialized

    });
};

const toggleEditingMode = () => {
    enableEditingMode.value = !enableEditingMode.value;
};

const onNodeTextChange = (node: RGNode, newNodeText: string) => {
    node.text = newNodeText;
};

onMounted(() => {
    showGraph();
});
</script>

<style scoped lang="scss">
::v-deep(.relation-graph) {
  .rel-map {
    background-size: 30px 30px;
    background-image: linear-gradient(90deg,rgba(0,0,0,.1) 1px,transparent 0),linear-gradient(180deg,rgba(0,0,0,.1) 1px,transparent 0);
  }
}
</style>
