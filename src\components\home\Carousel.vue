<template>
  <div class="carousel-container glass-card">
    <div class="carousel" ref="carouselRef">
      <div 
        v-for="(slide, index) in slides" 
        :key="index" 
        class="carousel-slide"
        :class="{ active: currentSlide === index }"
        :style="{ transform: `translateX(${100 * (index - currentSlide)}%)` }"
      >
        <div class="carousel-background" :class="slide.backgroundClass">
          <div class="background-pattern"></div>
          <div class="background-icon">
            <i :class="slide.icon"></i>
          </div>
        </div>
        <div class="carousel-caption glass-blur">
          <div class="caption-header">
            <div class="stats-badge glass-badge">{{ slide.stats }}</div>
            <h2 class="carousel-title">{{ slide.title }}</h2>
          </div>
          <p class="carousel-description">{{ slide.description }}</p>
          <div class="caption-actions">
            <router-link v-if="slide.link" :to="slide.link" class="btn glass-button primary">
              {{ slide.buttonText }}
              <span class="button-arrow">→</span>
            </router-link>
            <button class="btn glass-button secondary">了解详情</button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="carousel-controls">
      <button @click="prevSlide" class="carousel-control glass prev">&lt;</button>
      <div class="carousel-indicators">
        <button 
          v-for="(slide, index) in slides" 
          :key="index"
          @click="goToSlide(index)"
          class="carousel-indicator"
          :class="{ active: currentSlide === index }"
        ></button>
      </div>
      <button @click="nextSlide" class="carousel-control glass next">&gt;</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

// 更新轮播图数据，增加更丰富的内容和功能
const slides = ref([
  {
    title: '知识图谱可视化学习',
    description: '通过交互式知识图谱，直观地理解知识点之间的关联，支持多维度知识探索和智能导航',
    backgroundClass: 'bg-knowledge-graph',
    icon: 'fas fa-project-diagram',
    link: '/graph',
    buttonText: '立即体验',
    stats: '已有 1000+ 知识节点'
  },
  {
    title: '个性化学习路径',
    description: 'AI智能推荐系统根据您的学习情况、目标和偏好，制定最适合的个性化学习路径',
    backgroundClass: 'bg-learning-path',
    icon: 'fas fa-route',
    link: '/my',
    buttonText: '开始学习',
    stats: '平均提升效率 65%'
  },
  {
    title: '知识分享与协作',
    description: '与全球学习者分享您的知识图谱，共同构建和完善知识体系，促进知识的传播与创新',
    backgroundClass: 'bg-collaboration',
    icon: 'fas fa-users',
    link: '/share',
    buttonText: '查看分享',
    stats: '社区贡献者 500+'
  },
  {
    title: 'AI智能学习助手',
    description: '24/7在线AI助手为您提供智能答疑、学习建议、知识点解析和个性化辅导服务',
    backgroundClass: 'bg-ai-assistant',
    icon: 'fas fa-robot',
    link: '/assistant',
    buttonText: '体验助手',
    stats: '解答准确率 95%'
  },
  {
    title: '学习数据分析',
    description: '深度分析您的学习行为、进度和成效，提供详细的数据报告和智能化改进建议',
    backgroundClass: 'bg-analytics',
    icon: 'fas fa-chart-line',
    link: '/analytics',
    buttonText: '查看分析',
    stats: '数据维度 20+'
  }
]);

const carouselRef = ref(null);
const currentSlide = ref(0);
let autoplayInterval = null;

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % slides.value.length;
};

const prevSlide = () => {
  currentSlide.value = (currentSlide.value - 1 + slides.value.length) % slides.value.length;
};

const goToSlide = (index) => {
  currentSlide.value = index;
};

const startAutoplay = () => {
  stopAutoplay();
  autoplayInterval = setInterval(nextSlide, 5000);
};

const stopAutoplay = () => {
  if (autoplayInterval) {
    clearInterval(autoplayInterval);
  }
};

onMounted(() => {
  startAutoplay();
  
  // 鼠标悬停时暂停自动播放
  if (carouselRef.value) {
    carouselRef.value.addEventListener('mouseenter', stopAutoplay);
    carouselRef.value.addEventListener('mouseleave', startAutoplay);
  }
});

onBeforeUnmount(() => {
  stopAutoplay();
  
  // 移除事件监听
  if (carouselRef.value) {
    carouselRef.value.removeEventListener('mouseenter', stopAutoplay);
    carouselRef.value.removeEventListener('mouseleave', startAutoplay);
  }
});
</script>

<style scoped>
@import '../../assets/styles/glassmorphism.css';

.carousel-container {
  position: relative;
  width: 100%;
  max-width: 1200px; /* 进一步增加最大宽度 */
  height: 720px; /* 进一步增加高度 */
  margin: 0 auto; /* 居中显示 */
  overflow: hidden;
  border-radius: 20px;
  transition: all 0.3s ease;
  aspect-ratio: 5/3; /* 保持宽高比 */
}

.carousel {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.33, 1, 0.68, 1);
}

/* 替换图片的静态CSS背景设计 */
.carousel-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.carousel-background::before {
  content: '';
  position: absolute;
  top: -20%;
  left: -20%;
  width: 140%;
  height: 140%;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.25) 0%, transparent 40%),
    radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.2) 0%, transparent 60%),
    radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 0.2) 0%, transparent 45%);
  animation: ambientGlow 8s ease-in-out infinite alternate;
  z-index: 1;
}

.carousel-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 30%),
    linear-gradient(-45deg, rgba(255, 255, 255, 0.08) 70%, transparent 100%),
    radial-gradient(ellipse at center, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
  z-index: 2;
}

/* Context7风格的背景渐变 */
.bg-knowledge-graph {
  background: 
    /* 深层光效 */
    radial-gradient(ellipse at 30% 20%, rgba(59, 130, 246, 0.6) 0%, transparent 60%),
    radial-gradient(ellipse at 70% 80%, rgba(147, 51, 234, 0.5) 0%, transparent 50%),
    radial-gradient(circle at 20% 60%, rgba(236, 72, 153, 0.4) 0%, transparent 45%),
    /* 中层渐变 */
    conic-gradient(from 45deg at 60% 40%, rgba(59, 130, 246, 0.3) 0deg, transparent 120deg, rgba(147, 51, 234, 0.3) 240deg, transparent 360deg),
    /* 主背景 */
    linear-gradient(135deg, 
      rgba(59, 130, 246, 0.95) 0%,
      rgba(147, 51, 234, 0.85) 50%,
      rgba(236, 72, 153, 0.95) 100%);
}

.bg-learning-path {
  background: 
    /* 动态光带 */
    radial-gradient(ellipse at 15% 30%, rgba(16, 185, 129, 0.5) 0%, transparent 50%),
    radial-gradient(ellipse at 85% 70%, rgba(139, 92, 246, 0.4) 0%, transparent 45%),
    radial-gradient(circle at 50% 20%, rgba(59, 130, 246, 0.3) 0%, transparent 40%),
    /* 路径光效 */
    conic-gradient(from 45deg at 30% 70%, rgba(16, 185, 129, 0.3) 0deg, transparent 90deg, rgba(59, 130, 246, 0.25) 180deg, transparent 270deg),
    conic-gradient(from 225deg at 70% 30%, rgba(139, 92, 246, 0.3) 0deg, transparent 120deg),
    /* 主背景 */
    linear-gradient(135deg, 
      rgba(16, 185, 129, 0.95) 0%,
      rgba(59, 130, 246, 0.85) 50%,
      rgba(139, 92, 246, 0.95) 100%);
}

.bg-collaboration {
  background: 
    /* 协作光环 */
    radial-gradient(ellipse at 25% 25%, rgba(249, 115, 22, 0.5) 0%, transparent 55%),
    radial-gradient(ellipse at 75% 75%, rgba(236, 72, 153, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(239, 68, 68, 0.4) 0%, transparent 45%),
    radial-gradient(circle at 10% 90%, rgba(251, 191, 36, 0.3) 0%, transparent 40%),
    /* 团队连接光 */
    conic-gradient(from 0deg at 40% 60%, rgba(249, 115, 22, 0.3) 0deg 60deg, transparent 60deg 180deg, rgba(239, 68, 68, 0.25) 180deg 300deg, transparent 300deg),
    /* 主背景 */
    linear-gradient(135deg, 
      rgba(249, 115, 22, 0.95) 0%,
      rgba(239, 68, 68, 0.85) 50%,
      rgba(236, 72, 153, 0.95) 100%);
}

.bg-ai-assistant {
  background: 
    /* AI智能光效 */
    radial-gradient(ellipse at 20% 30%, rgba(168, 85, 247, 0.5) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 70%, rgba(59, 130, 246, 0.4) 0%, transparent 45%),
    radial-gradient(circle at 60% 20%, rgba(16, 185, 129, 0.4) 0%, transparent 40%),
    /* 电路脉冲 */
    conic-gradient(from 225deg at 25% 75%, rgba(168, 85, 247, 0.4) 0deg, transparent 120deg),
    conic-gradient(from 45deg at 75% 25%, rgba(16, 185, 129, 0.35) 0deg, transparent 120deg),
    /* 智能网格 */
    repeating-conic-gradient(from 0deg at 50% 50%, rgba(59, 130, 246, 0.2) 0deg 30deg, transparent 30deg 60deg),
    /* 主背景 */
    linear-gradient(135deg, 
      rgba(168, 85, 247, 0.95) 0%,
      rgba(59, 130, 246, 0.85) 50%,
      rgba(16, 185, 129, 0.95) 100%);
}

.bg-analytics {
  background: 
    /* 数据光谱 */
    radial-gradient(ellipse at 30% 20%, rgba(251, 191, 36, 0.5) 0%, transparent 50%),
    radial-gradient(ellipse at 70% 80%, rgba(249, 115, 22, 0.4) 0%, transparent 45%),
    radial-gradient(circle at 20% 70%, rgba(239, 68, 68, 0.4) 0%, transparent 40%),
    radial-gradient(circle at 90% 30%, rgba(236, 72, 153, 0.3) 0%, transparent 35%),
    /* 统计图形光 */
    repeating-conic-gradient(from 0deg at 30% 30%, rgba(251, 191, 36, 0.3) 0deg 30deg, transparent 30deg 60deg),
    repeating-conic-gradient(from 90deg at 70% 70%, rgba(239, 68, 68, 0.3) 0deg 45deg, transparent 45deg 90deg),
    /* 主背景 */
    linear-gradient(135deg, 
      rgba(251, 191, 36, 0.95) 0%,
      rgba(249, 115, 22, 0.85) 50%,
      rgba(239, 68, 68, 0.95) 100%);
}

/* 磨砂玻璃风格背景 */
.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    /* 磨砂玻璃主渐变 */
    linear-gradient(135deg, 
      rgba(255, 255, 255, 0.2) 0%,
      rgba(30, 64, 175, 0.05) 25%,
      rgba(251, 191, 36, 0.08) 50%,
      rgba(30, 64, 175, 0.04) 75%,
      rgba(255, 255, 255, 0.15) 100%),
    /* 玻璃光泽效果 */
    linear-gradient(45deg, 
      transparent 30%, 
      rgba(255, 255, 255, 0.1) 50%, 
      transparent 70%),
    /* 微妙装饰点 */
    radial-gradient(circle at 20% 30%, rgba(251, 191, 36, 0.06) 1px, transparent 2px),
    radial-gradient(circle at 80% 70%, rgba(30, 64, 175, 0.04) 1px, transparent 2px);
  background-size: 
    100% 100%,
    300% 300%,
    400px 400px, 350px 350px;
  backdrop-filter: blur(25px) saturate(1.5);
  -webkit-backdrop-filter: blur(25px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 8px 32px rgba(30, 64, 175, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  opacity: 0.9;
  animation: glassFloat 30s ease-in-out infinite;
  z-index: 3;
}

.background-pattern::before {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  bottom: 4px;
  background: 
    /* 内层玻璃光泽 */
    radial-gradient(ellipse at 30% 20%, rgba(255, 255, 255, 0.25) 0%, transparent 70%),
    radial-gradient(ellipse at 70% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 60%);
  background-size: 400px 400px, 350px 350px;
  border-radius: 12px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  animation: glassShimmer 20s ease-in-out infinite;
  z-index: 4;
}

.background-pattern::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.3) 0%, 
    rgba(255, 255, 255, 0.1) 50%, 
    rgba(255, 255, 255, 0.3) 100%);
  border-radius: 17px;
  opacity: 0.6;
  animation: glassBorder 25s ease-in-out infinite;
  z-index: 2;
}

/* 磨砂玻璃主题定制 */
.bg-knowledge-graph .background-pattern {
  /* 知识图谱：网络节点磨砂效果 */
  background: 
    /* 主玻璃渐变 */
    linear-gradient(135deg, 
      rgba(255, 255, 255, 0.25) 0%,
      rgba(30, 64, 175, 0.08) 30%,
      rgba(251, 191, 36, 0.12) 60%,
      rgba(255, 255, 255, 0.18) 100%),
    /* 网络节点装饰 */
    radial-gradient(circle at 30% 30%, rgba(30, 64, 175, 0.1) 2px, transparent 3px),
    radial-gradient(circle at 70% 70%, rgba(251, 191, 36, 0.08) 1.5px, transparent 2.5px);
  background-size: 100% 100%, 300px 300px, 250px 250px;
  animation: glassKnowledge 25s ease-in-out infinite;
}

.bg-knowledge-graph .background-pattern::before {
  background: 
    /* 知识节点光泽 */
    radial-gradient(ellipse at 25% 25%, rgba(255, 255, 255, 0.3) 0%, transparent 60%),
    radial-gradient(ellipse at 75% 75%, rgba(251, 191, 36, 0.15) 0%, transparent 50%);
  background-size: 300px 300px, 280px 280px;
  animation: glassNodePulse 18s ease-in-out infinite;
}

.bg-learning-path .background-pattern {
  /* 学习路径：教育磨砂玻璃 */
  background: 
    linear-gradient(135deg, 
      rgba(255, 255, 255, 0.18) 0%,
      rgba(251, 191, 36, 0.12) 40%,
      rgba(30, 64, 175, 0.08) 80%,
      rgba(255, 255, 255, 0.15) 100%),
    radial-gradient(circle at 40% 60%, rgba(251, 191, 36, 0.1) 1px, transparent 2px),
    radial-gradient(circle at 60% 40%, rgba(30, 64, 175, 0.06) 1px, transparent 2px);
  background-size: 100% 100%, 280px 280px, 320px 320px;
  animation: glassLearning 28s ease-in-out infinite;
}

.bg-learning-path .background-pattern::before {
  background: 
    radial-gradient(ellipse at 35% 25%, rgba(255, 255, 255, 0.35) 0%, transparent 65%),
    radial-gradient(ellipse at 65% 75%, rgba(251, 191, 36, 0.2) 0%, transparent 55%);
  background-size: 320px 320px, 280px 280px;
  animation: glassLearningGlow 22s ease-in-out infinite;
}

.bg-collaboration .background-pattern {
  /* 协作：团队磨砂玻璃 */
  background: 
    linear-gradient(135deg, 
      rgba(255, 255, 255, 0.22) 0%,
      rgba(240, 147, 251, 0.1) 35%,
      rgba(30, 64, 175, 0.06) 70%,
      rgba(255, 255, 255, 0.18) 100%),
    radial-gradient(circle at 35% 35%, rgba(240, 147, 251, 0.08) 1.5px, transparent 2.5px),
    radial-gradient(circle at 65% 65%, rgba(30, 64, 175, 0.05) 1px, transparent 2px);
  background-size: 100% 100%, 260px 260px, 300px 300px;
  animation: glassCollaboration 26s ease-in-out infinite;
}

.bg-collaboration .background-pattern::before {
  background: 
    radial-gradient(ellipse at 30% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 70%),
    radial-gradient(ellipse at 70% 30%, rgba(240, 147, 251, 0.15) 0%, transparent 60%);
  background-size: 300px 300px, 260px 260px;
  animation: glassTeamGlow 20s ease-in-out infinite;
}

.bg-ai-assistant .background-pattern {
  /* AI助手：智能磨砂玻璃 */
  background: 
    linear-gradient(135deg, 
      rgba(255, 255, 255, 0.2) 0%,
      rgba(79, 172, 254, 0.1) 30%,
      rgba(0, 242, 254, 0.08) 65%,
      rgba(255, 255, 255, 0.16) 100%),
    radial-gradient(circle at 30% 30%, rgba(79, 172, 254, 0.08) 1.5px, transparent 2.5px),
    radial-gradient(circle at 70% 70%, rgba(0, 242, 254, 0.06) 1px, transparent 2px);
  background-size: 100% 100%, 290px 290px, 320px 320px;
  animation: glassAI 24s ease-in-out infinite;
}

.bg-ai-assistant .background-pattern::before {
  background: 
    radial-gradient(ellipse at 35% 65%, rgba(255, 255, 255, 0.28) 0%, transparent 68%),
    radial-gradient(ellipse at 65% 35%, rgba(79, 172, 254, 0.18) 0%, transparent 58%);
  background-size: 280px 280px, 300px 300px;
  animation: glassAIGlow 19s ease-in-out infinite;
}

.bg-analytics .background-pattern {
  /* 数据分析：分析磨砂玻璃 */
  background: 
    linear-gradient(135deg, 
      rgba(255, 255, 255, 0.18) 0%,
      rgba(34, 197, 94, 0.08) 25%,
      rgba(251, 191, 36, 0.1) 50%,
      rgba(30, 64, 175, 0.06) 75%,
      rgba(255, 255, 255, 0.15) 100%),
    radial-gradient(circle at 25% 75%, rgba(34, 197, 94, 0.06) 1px, transparent 2px),
    radial-gradient(circle at 75% 25%, rgba(251, 191, 36, 0.08) 1.5px, transparent 2.5px);
  background-size: 100% 100%, 310px 310px, 270px 270px;
  animation: glassAnalytics 30s ease-in-out infinite;
}

.bg-analytics .background-pattern::before {
  background: 
    radial-gradient(ellipse at 35% 65%, rgba(255, 255, 255, 0.25) 0%, transparent 70%),
    radial-gradient(ellipse at 65% 35%, rgba(34, 197, 94, 0.15) 0%, transparent 60%);
  background-size: 300px 300px, 280px 280px;
  animation: glassAnalyticsGlow 23s ease-in-out infinite;
}

/* 中央图标增强 - 全新现代化重新设计 */
.background-icon {
  position: relative;
  z-index: 10;
  font-size: 4rem;
  font-weight: 900;
  transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: modernFloat 4s ease-in-out infinite;
  filter: drop-shadow(0 8px 20px rgba(0, 0, 0, 0.3));
  transform-style: preserve-3d;
  cursor: pointer;
  /* 高对比度白色背景 */
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 3px solid rgba(255, 255, 255, 1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 2px 4px rgba(255, 255, 255, 0.8);
}

/* 交互状态增强 */
.carousel-slide:hover .background-icon {
  transform: scale(1.15) rotateY(15deg);
  filter: drop-shadow(0 12px 30px rgba(0, 0, 0, 0.4));
  background: rgba(255, 255, 255, 1);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.3),
    inset 0 2px 8px rgba(255, 255, 255, 1);
}

.carousel-slide:hover .background-icon::before {
  opacity: 1;
  transform: scale(1.2);
}

.carousel-slide:hover .background-icon::after {
  opacity: 0.8;
  transform: scale(1.1) rotate(180deg);
}

/* 知识图谱图标 - 深蓝色 */
.bg-knowledge-graph .background-icon {
  color: #1e3a8a;
  animation: modernFloat 4s ease-in-out infinite, iconPulse 3s ease-in-out infinite;
}

/* 协作图标 - 深紫红色 */
.bg-collaboration .background-icon {
  color: #be185d;
  animation: modernFloat 4s ease-in-out infinite, iconBounce 2.5s ease-in-out infinite;
}

/* AI助手图标 - 深青色 */
.bg-ai-assistant .background-icon {
  color: #0369a1;
  animation: modernFloat 4s ease-in-out infinite, iconGlow 2s ease-in-out infinite alternate;
}

/* 数据分析图标 - 深橙色 */
.bg-analytics .background-icon {
  color: #ea580c;
  animation: modernFloat 4s ease-in-out infinite, iconScale 3s ease-in-out infinite;
}

/* 学习路径图标 - 深绿色 */
.bg-learning-path .background-icon {
  color: #047857;
  animation: modernFloat 4s ease-in-out infinite, iconShimmer 4s ease-in-out infinite;
}

/* 增强的多层背景光效 */
.background-icon::before {
  content: '';
  position: absolute;
  top: -40px;
  left: -40px;
  right: -40px;
  bottom: -40px;
  border-radius: 50%;
  z-index: -1;
  animation: iconHalo 6s ease-in-out infinite;
  filter: blur(25px);
  opacity: 0.8;
}

.bg-knowledge-graph .background-icon::before {
  background: radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, rgba(118, 75, 162, 0.3) 40%, transparent 80%);
}

.bg-collaboration .background-icon::before {
  background: radial-gradient(circle, rgba(240, 147, 251, 0.4) 0%, rgba(245, 87, 108, 0.3) 40%, transparent 80%);
}

.bg-ai-assistant .background-icon::before {
  background: radial-gradient(circle, rgba(79, 172, 254, 0.4) 0%, rgba(0, 242, 254, 0.3) 40%, transparent 80%);
}

.bg-analytics .background-icon::before {
  background: radial-gradient(circle, rgba(250, 112, 154, 0.4) 0%, rgba(254, 196, 87, 0.3) 40%, transparent 80%);
}

.bg-learning-path .background-icon::before {
  background: radial-gradient(circle, rgba(168, 237, 234, 0.4) 0%, rgba(210, 153, 194, 0.3) 40%, transparent 80%);
}

/* 动态装饰圆环 */
.background-icon::after {
  content: '';
  position: absolute;
  top: -50px;
  left: -50px;
  right: -50px;
  bottom: -50px;
  border: 3px solid;
  border-radius: 50%;
  z-index: -2;
  animation: iconRing 8s linear infinite;
  opacity: 0.6;
}

/* 为不同类型的图标设置不同的圆环颜色 */
.bg-knowledge-graph .background-icon::after {
  border-color: rgba(102, 126, 234, 0.4) rgba(118, 75, 162, 0.2) rgba(102, 126, 234, 0.4) rgba(118, 75, 162, 0.2);
}

.bg-collaboration .background-icon::after {
  border-color: rgba(240, 147, 251, 0.4) rgba(245, 87, 108, 0.2) rgba(240, 147, 251, 0.4) rgba(245, 87, 108, 0.2);
}

.bg-ai-assistant .background-icon::after {
  border-color: rgba(79, 172, 254, 0.4) rgba(0, 242, 254, 0.2) rgba(79, 172, 254, 0.4) rgba(0, 242, 254, 0.2);
}

.bg-analytics .background-icon::after {
  border-color: rgba(250, 112, 154, 0.4) rgba(254, 196, 87, 0.2) rgba(250, 112, 154, 0.4) rgba(254, 196, 87, 0.2);
}

.bg-learning-path .background-icon::after {
  border-color: rgba(168, 237, 234, 0.4) rgba(210, 153, 194, 0.2) rgba(168, 237, 234, 0.4) rgba(210, 153, 194, 0.2);
}

/* 现代化增强动画效果 */
@keyframes modernFloat {
  0%, 100% { 
    transform: translateY(0px) scale(1) rotate(0deg); 
  }
  25% { 
    transform: translateY(-6px) scale(1.03) rotate(1deg); 
  }
  50% { 
    transform: translateY(-12px) scale(1.06) rotate(0deg); 
  }
  75% { 
    transform: translateY(-6px) scale(1.03) rotate(-1deg); 
  }
}

@keyframes iconPulse {
  0%, 100% { 
    filter: brightness(1) drop-shadow(0 12px 40px rgba(102, 126, 234, 0.4)); 
    transform: scale(1);
  }
  50% { 
    filter: brightness(1.3) drop-shadow(0 20px 60px rgba(102, 126, 234, 0.7)); 
    transform: scale(1.08);
  }
}

@keyframes iconBounce {
  0%, 100% { 
    transform: translateY(0px) scale(1); 
    filter: brightness(1) drop-shadow(0 12px 40px rgba(240, 147, 251, 0.4)); 
  }
  25% { 
    transform: translateY(-8px) scale(1.05); 
    filter: brightness(1.15) drop-shadow(0 16px 50px rgba(240, 147, 251, 0.5)); 
  }
  50% { 
    transform: translateY(-16px) scale(1.1); 
    filter: brightness(1.3) drop-shadow(0 20px 60px rgba(240, 147, 251, 0.7)); 
  }
  75% { 
    transform: translateY(-8px) scale(1.05); 
    filter: brightness(1.15) drop-shadow(0 16px 50px rgba(240, 147, 251, 0.5)); 
  }
}

@keyframes iconRotate {
  0% { 
    transform: rotate(0deg) scale(1); 
  }
  25% { 
    transform: rotate(90deg) scale(1.05); 
  }
  50% { 
    transform: rotate(180deg) scale(1.1); 
  }
  75% { 
    transform: rotate(270deg) scale(1.05); 
  }
  100% { 
    transform: rotate(360deg) scale(1); 
  }
}

@keyframes iconGlow {
  0% { 
    filter: brightness(1) drop-shadow(0 12px 40px rgba(79, 172, 254, 0.4)); 
    text-shadow: 0 0 30px rgba(79, 172, 254, 0.6);
  }
  100% { 
    filter: brightness(1.4) drop-shadow(0 24px 80px rgba(79, 172, 254, 0.8)); 
    text-shadow: 0 0 50px rgba(79, 172, 254, 0.9);
  }
}

@keyframes iconScale {
  0%, 100% { 
    transform: scale(1) rotateY(0deg); 
    filter: brightness(1) drop-shadow(0 12px 40px rgba(250, 112, 154, 0.4)); 
  }
  25% { 
    transform: scale(1.06) rotateY(90deg); 
  }
  50% { 
    transform: scale(1.12) rotateY(180deg); 
    filter: brightness(1.3) drop-shadow(0 20px 60px rgba(250, 112, 154, 0.7)); 
  }
  75% { 
    transform: scale(1.06) rotateY(270deg); 
  }
}

@keyframes iconShimmer {
  0%, 100% { 
    filter: brightness(1) saturate(1) drop-shadow(0 12px 40px rgba(168, 237, 234, 0.4)) hue-rotate(0deg); 
  }
  25% { 
    filter: brightness(1.2) saturate(1.2) drop-shadow(0 16px 50px rgba(168, 237, 234, 0.6)) hue-rotate(45deg); 
  }
  50% { 
    filter: brightness(1.4) saturate(1.4) drop-shadow(0 20px 60px rgba(168, 237, 234, 0.8)) hue-rotate(90deg); 
  }
  75% { 
    filter: brightness(1.2) saturate(1.2) drop-shadow(0 16px 50px rgba(168, 237, 234, 0.6)) hue-rotate(135deg); 
  }
}

@keyframes iconHalo {
  0%, 100% { 
    transform: scale(1) rotate(0deg); 
    opacity: 0.4;
  }
  25% { 
    transform: scale(1.15) rotate(90deg); 
    opacity: 0.7;
  }
  50% { 
    transform: scale(1.3) rotate(180deg); 
    opacity: 0.9;
  }
  75% { 
    transform: scale(1.15) rotate(270deg); 
    opacity: 0.7;
  }
}

@keyframes iconRing {
  0% { 
    transform: rotate(0deg) scale(1); 
    opacity: 0.6;
  }
  25% { 
    transform: rotate(90deg) scale(1.08); 
    opacity: 0.4;
  }
  50% { 
    transform: rotate(180deg) scale(1.15); 
    opacity: 0.3;
  }
  75% { 
    transform: rotate(270deg) scale(1.08); 
    opacity: 0.4;
  }
  100% { 
    transform: rotate(360deg) scale(1); 
    opacity: 0.6;
  }
}

/* 大学教育风格动画效果 */
@keyframes academicFloat {
  0%, 100% { 
    transform: translateY(0px) scale(1); 
    backdrop-filter: blur(10px);
    opacity: 0.75;
  }
  25% { 
    transform: translateY(-3px) scale(1.008); 
    backdrop-filter: blur(12px);
    opacity: 0.78;
  }
  50% { 
    transform: translateY(-5px) scale(1.015); 
    backdrop-filter: blur(14px);
    opacity: 0.82;
  }
  75% { 
    transform: translateY(-3px) scale(1.008); 
    backdrop-filter: blur(12px);
    opacity: 0.78;
  }
}

@keyframes academicPath {
  0%, 100% { 
    opacity: 1;
    transform: translateX(0px) rotate(0deg);
  }
  25% { 
    opacity: 0.9;
    transform: translateX(2px) rotate(0.5deg);
  }
  50% { 
    opacity: 0.95;
    transform: translateX(0px) rotate(0deg);
  }
  75% { 
    opacity: 0.9;
    transform: translateX(-2px) rotate(-0.5deg);
  }
}

@keyframes academicWisdom {
  0%, 100% { 
    opacity: 0.8;
    transform: scale(1) rotate(0deg);
  }
  33% { 
    opacity: 0.9;
    transform: scale(1.02) rotate(120deg);
  }
  66% { 
    opacity: 0.85;
    transform: scale(1.01) rotate(240deg);
  }
}

@keyframes softShimmer {
  0%, 100% { 
    opacity: 0.4;
    transform: scale(1);
  }
  50% { 
    opacity: 0.7;
    transform: scale(1.02);
  }
}

@keyframes glassShimmer {
  0%, 100% { 
    opacity: 0.6;
    transform: scale(1);
  }
  50% { 
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes softBorder {
  0%, 100% { 
    opacity: 0.5;
    transform: rotate(0deg) scale(1);
  }
  25% { 
    opacity: 0.6;
    transform: rotate(90deg) scale(1.005);
  }
  50% { 
    opacity: 0.7;
    transform: rotate(180deg) scale(1.01);
  }
  75% { 
    opacity: 0.6;
    transform: rotate(270deg) scale(1.005);
  }
}

@keyframes glassBorder {
  0%, 100% { 
    opacity: 0.3;
    transform: rotate(0deg) scale(1);
  }
  25% { 
    opacity: 0.5;
    transform: rotate(90deg) scale(1.02);
  }
  50% { 
    opacity: 0.7;
    transform: rotate(180deg) scale(1.05);
  }
  75% { 
    opacity: 0.5;
    transform: rotate(270deg) scale(1.02);
  }
}

/* 大学教育风格专用动画 */
@keyframes academicKnowledge {
  0%, 100% { 
    opacity: 1;
    transform: translateX(0px) rotate(0deg);
  }
  25% { 
    opacity: 0.95;
    transform: translateX(1px) rotate(0.3deg);
  }
  50% { 
    opacity: 0.98;
    transform: translateX(0px) rotate(0deg);
  }
  75% { 
    opacity: 0.95;
    transform: translateX(-1px) rotate(-0.3deg);
  }
}

@keyframes academicCollaboration {
  0%, 100% { 
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
  30% { 
    opacity: 0.92;
    transform: scale(1.008) rotate(0.8deg);
  }
  60% { 
    opacity: 0.96;
    transform: scale(1.005) rotate(-0.4deg);
  }
  90% { 
    opacity: 0.94;
    transform: scale(1.003) rotate(0.2deg);
  }
}

@keyframes academicAI {
  0%, 100% { 
    opacity: 1;
    transform: translateY(0px) scale(1);
  }
  20% { 
    opacity: 0.93;
    transform: translateY(-1px) scale(1.005);
  }
  40% { 
    opacity: 0.97;
    transform: translateY(0px) scale(1.008);
  }
  60% { 
    opacity: 0.95;
    transform: translateY(1px) scale(1.003);
  }
  80% { 
    opacity: 0.98;
    transform: translateY(0px) scale(1.006);
  }
}

@keyframes academicAnalytics {
  0%, 100% { 
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
  25% { 
    opacity: 0.94;
    transform: rotate(0.5deg) scale(1.004);
  }
  50% { 
    opacity: 0.97;
    transform: rotate(0deg) scale(1.008);
  }
  75% { 
    opacity: 0.95;
    transform: rotate(-0.3deg) scale(1.002);
  }
}

@keyframes academicPulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
  33% { 
    opacity: 0.88;
    transform: scale(1.015) rotate(1deg);
  }
  66% { 
    opacity: 0.95;
    transform: scale(1.008) rotate(-0.5deg);
  }
}

@keyframes academicTeam {
  0%, 100% { 
    opacity: 1;
    transform: translateX(0px) translateY(0px);
  }
  25% { 
    opacity: 0.91;
    transform: translateX(1px) translateY(-1px);
  }
  50% { 
    opacity: 0.96;
    transform: translateX(-0.5px) translateY(1px);
  }
  75% { 
    opacity: 0.93;
    transform: translateX(0.5px) translateY(-0.5px);
  }
}

@keyframes academicDataFlow {
  0%, 100% { 
    opacity: 1;
    transform: translateY(0px) scale(1);
  }
  20% { 
    opacity: 0.89;
    transform: translateY(-2px) scale(1.012);
  }
  40% { 
    opacity: 0.94;
    transform: translateY(1px) scale(1.006);
  }
  60% { 
    opacity: 0.91;
    transform: translateY(-1px) scale(1.009);
  }
  80% { 
    opacity: 0.97;
    transform: translateY(0.5px) scale(1.003);
  }
}

@keyframes academicVisualization {
  0%, 100% { 
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
  30% { 
    opacity: 0.92;
    transform: rotate(1.2deg) scale(1.007);
  }
  60% { 
    opacity: 0.96;
    transform: rotate(-0.8deg) scale(1.004);
  }
  90% { 
    opacity: 0.94;
    transform: rotate(0.4deg) scale(1.001);
  }
}

/* 磨砂玻璃动画效果 */
@keyframes glassFloat {
  0%, 100% { 
    opacity: 0.9;
    transform: translateY(0px) scale(1);
    filter: blur(25px) saturate(1.5);
  }
  25% { 
    opacity: 0.95;
    transform: translateY(-2px) scale(1.002);
    filter: blur(22px) saturate(1.6);
  }
  50% { 
    opacity: 0.88;
    transform: translateY(1px) scale(1.001);
    filter: blur(28px) saturate(1.4);
  }
  75% { 
    opacity: 0.92;
    transform: translateY(-1px) scale(1.003);
    filter: blur(24px) saturate(1.7);
  }
}

@keyframes glassKnowledge {
  0%, 100% { 
    opacity: 1;
    transform: translateX(0px) rotate(0deg);
    background-position: 0% 0%, 0% 0%, 0% 0%;
  }
  33% { 
    opacity: 0.95;
    transform: translateX(1px) rotate(0.5deg);
    background-position: 10% 10%, 5% 5%, 15% 15%;
  }
  66% { 
    opacity: 0.98;
    transform: translateX(-0.5px) rotate(-0.3deg);
    background-position: -5% -5%, 8% 8%, -10% -10%;
  }
}

@keyframes glassNodePulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
    background-size: 300px 300px, 280px 280px;
  }
  50% { 
    opacity: 0.85;
    transform: scale(1.015);
    background-size: 320px 320px, 300px 300px;
  }
}

@keyframes glassLearning {
  0%, 100% { 
    opacity: 1;
    transform: translateY(0px);
    background-position: 0% 0%, 0% 0%, 0% 0%;
  }
  40% { 
    opacity: 0.92;
    transform: translateY(-1px);
    background-position: 5% 5%, 10% 10%, -5% -5%;
  }
  80% { 
    opacity: 0.96;
    transform: translateY(0.5px);
    background-position: -3% -3%, -5% -5%, 8% 8%;
  }
}

@keyframes glassLearningGlow {
  0%, 100% { 
    opacity: 1;
    background-size: 320px 320px, 280px 280px;
  }
  50% { 
    opacity: 0.8;
    background-size: 340px 340px, 300px 300px;
  }
}

@keyframes glassCollaboration {
  0%, 100% { 
    opacity: 1;
    transform: rotate(0deg);
    background-position: 0% 0%, 0% 0%, 0% 0%;
  }
  35% { 
    opacity: 0.88;
    transform: rotate(0.8deg);
    background-position: 8% 8%, 12% 12%, -8% -8%;
  }
  70% { 
    opacity: 0.94;
    transform: rotate(-0.4deg);
    background-position: -6% -6%, -10% -10%, 15% 15%;
  }
}

@keyframes glassTeamGlow {
  0%, 100% { 
    opacity: 1;
    background-size: 300px 300px, 260px 260px;
    filter: blur(5px);
  }
  50% { 
    opacity: 0.75;
    background-size: 320px 320px, 280px 280px;
    filter: blur(8px);
  }
}

@keyframes glassAI {
  0%, 100% { 
    opacity: 1;
    transform: translateX(0px) scale(1);
    background-position: 0% 0%, 0% 0%, 0% 0%;
  }
  30% { 
    opacity: 0.85;
    transform: translateX(1.5px) scale(1.008);
    background-position: 12% 12%, 8% 8%, -12% -12%;
  }
  60% { 
    opacity: 0.92;
    transform: translateX(-1px) scale(1.004);
    background-position: -8% -8%, 15% 15%, 10% 10%;
  }
}

@keyframes glassAIGlow {
  0%, 100% { 
    opacity: 1;
    background-size: 280px 280px, 300px 300px;
    filter: blur(5px);
  }
  50% { 
    opacity: 0.7;
    background-size: 300px 300px, 320px 320px;
    filter: blur(12px);
  }
}

@keyframes glassAnalytics {
  0%, 100% { 
    opacity: 1;
    transform: translateY(0px) rotate(0deg);
    background-position: 0% 0%, 0% 0%, 0% 0%;
  }
  25% { 
    opacity: 0.9;
    transform: translateY(-1px) rotate(0.3deg);
    background-position: 6% 6%, 10% 10%, -6% -6%;
  }
  75% { 
    opacity: 0.95;
    transform: translateY(1px) rotate(-0.2deg);
    background-position: -4% -4%, -8% -8%, 12% 12%;
  }
}

@keyframes glassAnalyticsGlow {
  0%, 100% { 
    opacity: 1;
    background-size: 300px 300px, 280px 280px;
  }
  50% { 
    opacity: 0.8;
    background-size: 320px 320px, 300px 300px;
  }
}

.carousel-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-sm);
  padding-bottom: 50px; /* 为控制按钮留出空间 */
  color: white;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.6);
  z-index: 5;
}

.caption-header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.stats-badge {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 0.75rem;
  font-weight: 500;
  align-self: flex-start;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.caption-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  flex-wrap: wrap;
}

.glass-button.primary {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.glass-button.secondary {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.button-arrow {
  margin-left: 6px;
  transition: transform 0.3s ease;
}

.glass-button:hover .button-arrow {
  transform: translateX(3px);
}

.carousel-title {
  font-size: var(--font-size-xlarge);
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
}

.carousel-description {
  font-size: var(--font-size-normal);
  margin-bottom: var(--spacing-md);
  opacity: 0.9;
  line-height: 1.5;
}

.glass-button {
  display: inline-block;
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.carousel-controls {
  position: absolute;
  bottom: var(--spacing-xs);
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  z-index: 50;
  pointer-events: none;
}

.carousel-control {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 2px solid rgba(255, 255, 255, 0.5);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px;
  transition: all 0.3s ease;
  z-index: 100;
  pointer-events: auto;
  position: relative;
  padding: 12px;
  margin: 8px;
}

.carousel-control::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  pointer-events: auto;
}

.carousel-control:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

.carousel-control:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.4);
}

.carousel-indicators {
  display: flex;
  gap: var(--spacing-xs);
  pointer-events: auto;
  z-index: 100;
  position: relative;
}

.carousel-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 100;
  pointer-events: auto;
  position: relative;
}

.carousel-indicator:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

.carousel-indicator.active {
  background-color: white;
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

/* 适配移动设备 */
@media (max-width: 800px) {
  .carousel-container {
    height: 450px; /* 调整移动端高度，保持合理比例 */
    max-width: 100%; /* 移动端使用全宽 */
    aspect-ratio: 4/3; /* 移动端使用更适合的宽高比 */
  }
  
  .carousel-title {
    font-size: var(--font-size-large);
  }
  
  .carousel-description {
    font-size: var(--font-size-small);
  }
  
  .background-icon {
    font-size: 4.2rem; /* 移动端也相应增大 */
    filter: drop-shadow(0 8px 30px rgba(0, 0, 0, 0.2));
  }
  
  .background-icon::before {
    top: -30px;
    left: -30px;
    right: -30px;
    bottom: -30px;
    filter: blur(20px);
  }
  
  .background-icon::after {
    top: -35px;
    left: -35px;
    right: -35px;
    bottom: -35px;
    border-width: 2px;
  }
  
  .carousel-caption {
    padding: var(--spacing-xs);
    padding-bottom: 35px;
  }
}

/* 平板设备适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .carousel-container {
    height: 600px; /* 增加平板端高度 */
    max-width: 950px; /* 增加平板端最大宽度 */
  }
}

/* 小屏手机适配 */
@media (max-width: 480px) {
  .carousel-container {
    height: 320px; /* 小屏手机高度 */
    aspect-ratio: 1/1; /* 小屏手机使用正方形比例 */
  }
  
  .carousel-title {
    font-size: var(--font-size-medium);
  }
  
  .background-icon {
    font-size: 3.5rem; /* 小屏手机也相应增大 */
    filter: drop-shadow(0 6px 25px rgba(0, 0, 0, 0.15));
  }
  
  .background-icon::before {
    top: -25px;
    left: -25px;
    right: -25px;
    bottom: -25px;
    filter: blur(15px);
  }
  
  .background-icon::after {
    top: -30px;
    left: -30px;
    right: -30px;
    bottom: -30px;
    border-width: 2px;
  }
  
  .caption-actions {
    flex-direction: column;
  }
  
  .glass-button {
    padding: 8px 16px;
    font-size: 0.875rem;
  }
}
</style> 