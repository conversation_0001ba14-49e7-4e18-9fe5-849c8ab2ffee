<template>
    <div class="my-page">
      <div class="container py-3 full-width">
        <div class="page-header">
          <router-link to="/" class="back-to-home-btn">
            <i class="back-icon"></i>
            <span>返回首页</span>
          </router-link>
          <h1 class="page-title">AI智能OBE智慧教学平台</h1>
          <div class="header-subtitle">学生学习中心</div>
        </div>
        
        <!-- 左侧菜单和主内容区 -->
        <div class="content-layout">
          <!-- 左侧菜单 -->
          <div class="sidebar">
            <div class="sidebar-menu">
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'courses' }" 
                @click="activeTab = 'courses'"
              >
                <i class="menu-icon courses-icon"></i>
                <span class="menu-text">我的课程</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'knowledge-graph' }" 
                @click="activeTab = 'knowledge-graph'"
              >
                <i class="menu-icon knowledge-graph-icon"></i>
                <span class="menu-text">知识图谱</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'homework' }" 
                @click="activeTab = 'homework'"
              >
                <i class="menu-icon homework-icon"></i>
                <span class="menu-text">作业</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'discussion' }" 
                @click="activeTab = 'discussion'"
              >
                <i class="menu-icon discussion-icon"></i>
                <span class="menu-text">讨论</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'group' }" 
                @click="activeTab = 'group'"
              >
                <i class="menu-icon group-icon"></i>
                <span class="menu-text">小组</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'ai-assistant' }" 
                @click="activeTab = 'ai-assistant'"
              >
                <i class="menu-icon ai-assistant-icon"></i>
                <span class="menu-text">AI助手</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'materials' }" 
                @click="activeTab = 'materials'"
              >
                <i class="menu-icon materials-icon"></i>
                <span class="menu-text">学习资料</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'settings' }" 
                @click="activeTab = 'settings'"
              >
                <i class="menu-icon settings-icon"></i>
                <span class="menu-text">设置</span>
              </div>
            </div>
          </div>
          
          <!-- 主内容区域 -->
          <div class="main-content">
            <!-- 我的课程面板 -->
            <div v-if="activeTab === 'courses'" class="courses-panel">
              <!-- 课程筛选标签 -->
              <div class="filter-tabs">
                <div 
                  class="filter-tab" 
                  :class="{ active: courseFilter === 'all' }" 
                  @click="courseFilter = 'all'"
                >
                  全部课程
                </div>
                <div 
                  class="filter-tab" 
                  :class="{ active: courseFilter === 'ongoing' }" 
                  @click="courseFilter = 'ongoing'"
                >
                  进行中
                </div>
                <div 
                  class="filter-tab" 
                  :class="{ active: courseFilter === 'completed' }" 
                  @click="courseFilter = 'completed'"
                >
                  已完成
                </div>
              </div>
              
              <!-- 课程列表 -->
              <div v-if="filteredCourses.length > 0" class="courses-grid">
                <div 
                  v-for="course in filteredCourses" 
                  :key="course.id"
                  :class="['course-card', `course-card-${course.type}`]"
                  @click="enterCourse(course.id)"
                >
                  <div class="course-header">
                    <h3 class="course-title">{{ course.title }}</h3>
                    <div class="course-teacher">{{ course.teacher }}</div>
                  </div>
                  <div class="course-stats">
                    <div class="stat-item">
                      <span class="stat-label">学习进度</span>
                      <span class="stat-value">{{ course.progress }}%</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">总课时</span>
                      <span class="stat-value">{{ course.totalHours }}学时</span>
                    </div>
                  </div>
                  <div class="progress-section">
                    <div class="progress-label">进度: {{ course.progress }}%</div>
                    <div class="progress-bar">
                      <div class="progress-fill" :style="`width: ${course.progress}%`"></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 空状态 -->
              <div v-else class="empty-state">
                <div class="empty-icon">📚</div>
                <h3 class="empty-title">暂无课程</h3>
                <p class="empty-description">您还没有注册任何课程，请联系老师或管理员添加课程</p>
              </div>
            </div>
            
            <!-- 知识图谱面板 -->
            <div v-if="activeTab === 'knowledge-graph'" class="knowledge-graph-panel">
              <!-- 子标签导航 -->
              <div class="sub-tabs">
                <div 
                  class="sub-tab-item" 
                  :class="{ active: subActiveTab === 'outline' }" 
                  @click="subActiveTab = 'outline'"
                >
                  <i class="sub-tab-icon outline-icon"></i>
                  <span>知识大纲</span>
                </div>
                <div 
                  class="sub-tab-item" 
                  :class="{ active: subActiveTab === 'graphview' }" 
                  @click="subActiveTab = 'graphview'"
                >
                  <i class="sub-tab-icon list-icon"></i>
                  <span>图谱列表</span>
                </div>
              </div>
              
              <!-- 知识大纲内容 -->
              <div v-if="subActiveTab === 'outline'" class="sub-panel outline-content">
                <div v-if="selectedOutline" class="selected-outline-container">
                  <div class="outline-header">
                    <button class="back-button" @click="selectedOutline = null">
                      <i class="back-icon"></i>
                      <span>返回大纲列表</span>
                    </button>
                    <h3 class="outline-title">{{ selectedOutline.title }}</h3>
                  </div>
                  <iframe :src="`/outline?embedded=true&onlyOutline=true&courseId=${selectedOutline.id}`" class="embedded-view"></iframe>
                </div>
                <MyOutlineList v-else @select-outline="handleOutlineSelect" />
              </div>
              
              <!-- 知识图谱列表内容 -->
              <div v-if="subActiveTab === 'graphview'" class="sub-panel graphview-content">
                <div v-if="selectedGraph" class="selected-graph-container">
                  <div class="graph-header">
                    <button class="back-button" @click="selectedGraph = null">
                      <i class="back-icon"></i>
                      <span>返回图谱列表</span>
                    </button>
                    <h3 class="graph-title">{{ selectedGraph.title }}</h3>
                  </div>
                  <iframe :src="`/graph?embedded=true&courseId=${selectedGraph.id}`" class="embedded-view"></iframe>
                </div>
                <MyGraphList v-else @select-graph="handleGraphSelect" />
              </div>
            </div>
            
            <!-- 作业面板 -->
            <div v-if="activeTab === 'homework'" class="homework-panel">
              <!-- 作业筛选标签 -->
              <div class="filter-tabs">
                <div 
                  class="filter-tab" 
                  :class="{ active: homeworkFilter === 'all' }" 
                  @click="homeworkFilter = 'all'"
                >
                  全部作业
                </div>
                <div 
                  class="filter-tab" 
                  :class="{ active: homeworkFilter === 'pending' }" 
                  @click="homeworkFilter = 'pending'"
                >
                  待完成
                </div>
                <div 
                  class="filter-tab" 
                  :class="{ active: homeworkFilter === 'submitted' }" 
                  @click="homeworkFilter = 'submitted'"
                >
                  已提交
                </div>
                <div 
                  class="filter-tab" 
                  :class="{ active: homeworkFilter === 'graded' }" 
                  @click="homeworkFilter = 'graded'"
                >
                  已批改
                </div>
              </div>
              
              <!-- 作业列表 -->
              <div v-if="filteredHomework.length > 0" class="homework-list">
                <div 
                  v-for="homework in filteredHomework" 
                  :key="homework.id"
                  class="homework-item"
                  @click="viewHomework(homework.id)"
                >
                  <div class="homework-header">
                    <h3 class="homework-title">{{ homework.title }}</h3>
                    <div class="homework-status" :class="homework.status">
                      {{ getHomeworkStatusText(homework.status) }}
                    </div>
                  </div>
                  <div class="homework-info">
                    <div class="info-item">
                      <span class="info-label">课程:</span>
                      <span class="info-value">{{ homework.courseName }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">截止时间:</span>
                      <span class="info-value">{{ homework.deadline }}</span>
                    </div>
                    <div class="info-item" v-if="homework.score">
                      <span class="info-label">成绩:</span>
                      <span class="info-value">{{ homework.score }}分</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 空状态 -->
              <div v-else class="empty-state">
                <div class="empty-icon">📝</div>
                <h3 class="empty-title">暂无作业</h3>
                <p class="empty-description">当前没有符合条件的作业</p>
              </div>
            </div>

            <!-- 讨论面板 -->
            <div v-if="activeTab === 'discussion'" class="discussion-panel">
              <div class="discussion-list">
                <div
                  v-for="discussion in discussions"
                  :key="discussion.id"
                  class="discussion-item"
                  @click="viewDiscussion(discussion.id)"
                >
                  <div class="discussion-header">
                    <h3 class="discussion-title">{{ discussion.title }}</h3>
                    <div class="discussion-course">{{ discussion.courseName }}</div>
                  </div>
                  <div class="discussion-info">
                    <div class="info-item">
                      <span class="info-label">发起人:</span>
                      <span class="info-value">{{ discussion.author }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">回复数:</span>
                      <span class="info-value">{{ discussion.replyCount }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">最后回复:</span>
                      <span class="info-value">{{ discussion.lastReply }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 小组面板 -->
            <div v-if="activeTab === 'group'" class="group-panel">
              <div class="group-list">
                <div
                  v-for="group in groups"
                  :key="group.id"
                  class="group-item"
                  @click="viewGroup(group.id)"
                >
                  <div class="group-header">
                    <h3 class="group-title">{{ group.name }}</h3>
                    <div class="group-members">{{ group.memberCount }}人</div>
                  </div>
                  <div class="group-info">
                    <div class="info-item">
                      <span class="info-label">课程:</span>
                      <span class="info-value">{{ group.courseName }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">组长:</span>
                      <span class="info-value">{{ group.leader }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">创建时间:</span>
                      <span class="info-value">{{ group.createTime }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- AI助手面板 -->
            <div v-if="activeTab === 'ai-assistant'" class="ai-assistant-panel">
              <div class="ai-modules-grid">
                <div class="module-card ai-chat-card" @click="handleAIChatAssistant">
                  <div class="module-header">
                    <div class="module-icon ai-chat-icon">
                      <i class="icon-ai-chat"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">AI学习助手</h3>
                    <p class="module-desc">智能问答助手，随时为您解答学习中的疑问</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-blue">开始对话</button>
                  </div>
                </div>

                <div class="module-card ai-tutor-card" @click="handleAITutor">
                  <div class="module-header">
                    <div class="module-icon ai-tutor-icon">
                      <i class="icon-ai-tutor"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">AI学习指导</h3>
                    <p class="module-desc">个性化学习建议和学习路径规划</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-green">获取指导</button>
                  </div>
                </div>

                <div class="module-card ai-practice-card" @click="handleAIPractice">
                  <div class="module-header">
                    <div class="module-icon ai-practice-icon">
                      <i class="icon-ai-practice"></i>
                    </div>
                  </div>
                  <div class="module-content">
                    <h3 class="module-title">AI练习生成</h3>
                    <p class="module-desc">根据学习进度智能生成练习题目</p>
                  </div>
                  <div class="module-footer">
                    <button class="module-btn btn-orange">开始练习</button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 学习资料面板 -->
            <div v-if="activeTab === 'materials'" class="materials-panel">
              <div class="materials-grid">
                <div class="material-category-card course-materials-card" @click="viewCourseMaterials">
                  <div class="category-icon">📁</div>
                  <div class="category-info">
                    <h3 class="category-title">课程资料</h3>
                    <p class="category-count">{{ courseMaterials.count }}个文件</p>
                  </div>
                </div>

                <div class="material-category-card shared-resources-card" @click="viewSharedResources">
                  <div class="category-icon">📤</div>
                  <div class="category-info">
                    <h3 class="category-title">教师共享</h3>
                    <p class="category-count">{{ sharedResources.count }}个文件</p>
                  </div>
                </div>

                <div class="material-category-card video-resources-card" @click="viewVideoResources">
                  <div class="category-icon">🎥</div>
                  <div class="category-info">
                    <h3 class="category-title">视频资源</h3>
                    <p class="category-count">{{ videoResources.count }}个文件</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 设置面板 -->
            <div v-if="activeTab === 'settings'" class="settings-panel">
              <MySettings />
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, onMounted } from 'vue';
  import { getCurrentUser } from '@/api/auth';
  import MySettings from '@/components/my/MySettings.vue';
  import MyOutlineList from '@/components/my/MyOutlineList.vue';
  import MyGraphList from '@/components/my/MyGraphList.vue';
  import { useRoute } from 'vue-router';
  
  const route = useRoute();
  
  // 当前用户信息
  const currentUser = ref(getCurrentUser() || {});
  
  // 是否为学生角色
  const isStudent = computed(() => {
    return currentUser.value.role === 'student';
  });
  
  // 当前激活的标签页 - 默认显示课程
  const activeTab = ref('courses');
  
  // 子标签页状态
  const subActiveTab = ref('outline');
  
  // 课程筛选状态
  const courseFilter = ref('ongoing');
  
  // 作业筛选状态
  const homeworkFilter = ref('pending');
  
  // 组件挂载时加载用户数据
  onMounted(async () => {
    // TODO: 从API获取最新的用户数据
    console.log('加载学生用户数据');
    
    // 检查URL参数中是否指定了要显示的标签页
    if (route.query.tab) {
      activeTab.value = route.query.tab;
    }
  });
  
  const selectedOutline = ref(null);
  const selectedGraph = ref(null);
  
  const handleOutlineSelect = (outline) => {
    selectedOutline.value = outline;
  };
  
  const handleGraphSelect = (graph) => {
    selectedGraph.value = graph;
  };

  // 学生课程数据
  const courses = ref([
    {
      id: 1,
      title: '土木工程概论',
      teacher: '张教授',
      progress: 75,
      totalHours: 48,
      type: 'blue',
      status: 'ongoing'
    },
    {
      id: 2,
      title: '有限元分析',
      teacher: '李教授',
      progress: 60,
      totalHours: 36,
      type: 'green',
      status: 'ongoing'
    },
    {
      id: 3,
      title: '结构力学',
      teacher: '王教授',
      progress: 100,
      totalHours: 64,
      type: 'blue',
      status: 'completed'
    },
    {
      id: 4,
      title: '建筑材料学',
      teacher: '赵教授',
      progress: 45,
      totalHours: 32,
      type: 'green',
      status: 'ongoing'
    }
  ]);

  // 学生作业数据
  const homework = ref([
    {
      id: 1,
      title: '土木工程基础知识测试',
      courseName: '土木工程概论',
      deadline: '2024-12-15',
      status: 'pending',
      score: null
    },
    {
      id: 2,
      title: '有限元分析实验报告',
      courseName: '有限元分析',
      deadline: '2024-12-10',
      status: 'submitted',
      score: null
    },
    {
      id: 3,
      title: '结构力学计算题',
      courseName: '结构力学',
      deadline: '2024-11-30',
      status: 'graded',
      score: 85
    }
  ]);

  // 讨论数据
  const discussions = ref([
    {
      id: 1,
      title: '关于结构力学学习方法的讨论',
      courseName: '结构力学',
      author: '张同学',
      replyCount: 15,
      lastReply: '2小时前'
    },
    {
      id: 2,
      title: '有限元分析软件使用心得',
      courseName: '有限元分析',
      author: '李同学',
      replyCount: 8,
      lastReply: '1天前'
    }
  ]);

  // 小组数据
  const groups = ref([
    {
      id: 1,
      name: '结构力学学习小组',
      courseName: '结构力学',
      leader: '王同学',
      memberCount: 6,
      createTime: '2024-11-01'
    },
    {
      id: 2,
      name: '土木工程项目组',
      courseName: '土木工程概论',
      leader: '赵同学',
      memberCount: 8,
      createTime: '2024-10-15'
    }
  ]);

  // 学习资料数据
  const courseMaterials = ref({
    count: 25,
    files: []
  });

  const sharedResources = ref({
    count: 18,
    files: []
  });

  const videoResources = ref({
    count: 12,
    files: []
  });

  // 筛选后的课程
  const filteredCourses = computed(() => {
    if (courseFilter.value === 'all') {
      return courses.value;
    }
    return courses.value.filter(course => course.status === courseFilter.value);
  });

  // 筛选后的作业
  const filteredHomework = computed(() => {
    if (homeworkFilter.value === 'all') {
      return homework.value;
    }
    return homework.value.filter(hw => hw.status === homeworkFilter.value);
  });

  // 课程相关方法
  const enterCourse = (courseId) => {
    console.log('进入课程:', courseId);
    // TODO: 跳转到课程详情页
  };

  // 作业相关方法
  const viewHomework = (homeworkId) => {
    console.log('查看作业:', homeworkId);
    // TODO: 跳转到作业详情页
  };

  const getHomeworkStatusText = (status) => {
    const statusMap = {
      'pending': '待完成',
      'submitted': '已提交',
      'graded': '已批改'
    };
    return statusMap[status] || status;
  };

  // 讨论相关方法
  const viewDiscussion = (discussionId) => {
    console.log('查看讨论:', discussionId);
    // TODO: 跳转到讨论详情页
  };

  // 小组相关方法
  const viewGroup = (groupId) => {
    console.log('查看小组:', groupId);
    // TODO: 跳转到小组详情页
  };

  // AI助手相关方法
  const handleAIChatAssistant = () => {
    console.log('AI学习助手');
    // TODO: 跳转到AI对话页面
  };

  const handleAITutor = () => {
    console.log('AI学习指导');
    // TODO: 跳转到AI学习指导页面
  };

  const handleAIPractice = () => {
    console.log('AI练习生成');
    // TODO: 跳转到AI练习页面
  };

  // 学习资料相关方法
  const viewCourseMaterials = () => {
    console.log('查看课程资料');
    // TODO: 跳转到课程资料列表
  };

  const viewSharedResources = () => {
    console.log('查看教师共享资源');
    // TODO: 跳转到共享资源列表
  };

  const viewVideoResources = () => {
    console.log('查看视频资源');
    // TODO: 跳转到视频资源列表
  };
  </script>

  <style scoped>
  /* 基础页面样式 */
  .my-page {
    min-height: 100vh;
    background-color: var(--background-color-secondary);
    margin: 0;
    padding: 0;
  }

  .full-width {
    max-width: 100% !important;
    padding-left: 0;
    padding-right: 0;
    margin: 0;
    width: 100%;
  }

  .page-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0.5rem;
  }

  .back-to-home-btn {
    display: flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    margin-right: 1rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
  }

  .back-to-home-btn:hover {
    background-color: var(--hover-color);
  }

  .back-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    position: relative;
  }

  .back-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 12px;
    height: 12px;
    border-left: 2px solid var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    transform: translateY(-50%) rotate(45deg);
  }

  .page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }

  .header-subtitle {
    font-size: 0.9375rem;
    color: var(--text-color-secondary);
    margin-top: 0.25rem;
  }

  /* 布局样式 */
  .content-layout {
    display: flex;
    gap: 0;
    min-height: 600px;
    height: 100vh;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .sidebar {
    width: 180px;
    flex-shrink: 0;
    background-color: #f5f5f5;
    border-radius: 0;
    box-shadow: none;
    padding: 0.5rem 0;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
  }

  .sidebar-menu {
    display: flex;
    flex-direction: column;
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: none;
    margin-bottom: 4px;
    border-radius: 0;
  }

  .menu-item:hover {
    background-color: #e9e9e9;
  }

  .menu-item.active {
    background-color: #e9e9e9;
    color: var(--primary-color);
    font-weight: 500;
    border-left: 4px solid var(--primary-color);
  }

  .menu-icon {
    width: 20px;
    height: 20px;
    margin-right: 0.75rem;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.7;
  }

  .menu-item.active .menu-icon {
    opacity: 1;
  }

  .menu-text {
    font-size: 0.9375rem;
    font-weight: 400;
  }

  .main-content {
    flex: 1;
    background-color: #ffffff;
    border-radius: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 1rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    width: calc(100% - 180px);
  }

  /* 面板通用样式 */
  .courses-panel, .knowledge-graph-panel, .homework-panel, .discussion-panel, .group-panel, .ai-assistant-panel, .materials-panel, .settings-panel {
    flex: 1;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  /* 筛选标签样式 */
  .filter-tabs {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    margin-bottom: 1.5rem;
  }

  .filter-tab {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    font-weight: 500;
  }

  .filter-tab:hover {
    color: var(--primary-color);
    background-color: rgba(66, 153, 225, 0.05);
  }

  .filter-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: rgba(66, 153, 225, 0.1);
  }

  /* 课程网格样式 */
  .courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
    overflow-y: auto;
  }

  .course-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
  }

  .course-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .course-card-blue {
    border-left: 4px solid #3B82F6;
  }

  .course-card-green {
    border-left: 4px solid #10B981;
  }

  .course-header {
    margin-bottom: 1rem;
  }

  .course-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
  }

  .course-teacher {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .course-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .stat-label {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-bottom: 0.25rem;
  }

  .stat-value {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
  }

  .progress-section {
    margin-top: 1rem;
  }

  .progress-label {
    font-size: 0.875rem;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  /* 作业列表样式 */
  .homework-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow-y: auto;
  }

  .homework-item {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
  }

  .homework-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .homework-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .homework-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    flex: 1;
  }

  .homework-status {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 1rem;
  }

  .homework-status.pending {
    background: #fef3c7;
    color: #d97706;
  }

  .homework-status.submitted {
    background: #dbeafe;
    color: #2563eb;
  }

  .homework-status.graded {
    background: #d1fae5;
    color: #059669;
  }

  .homework-info {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .info-label {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .info-value {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1f2937;
  }

  /* 空状态样式 */
  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    margin-top: 1rem;
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.6;
  }

  .empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 1rem 0;
  }

  .empty-description {
    font-size: 1rem;
    color: var(--text-color-secondary);
    margin: 0 0 2rem 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  /* 菜单图标样式 */
  .courses-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M12 3L1 9l11 6 9-4.91V17h2V9L12 3zm0 11.13L6.08 12 12 9.87 17.92 12 12 14.13z'/%3E%3C/svg%3E");
  }

  .knowledge-graph-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19.5 5.5v13h-15v-13h15m0-2h-15c-1.1 0-2 .9-2 2v13c0 1.1.9 2 2 2h15c1.1 0 2-.9 2-2v-13c0-1.1-.9-2-2-2zM9 8h2v8H9zm4 3h2v5h-2zm-8 0h2v5H5z'/%3E%3C/svg%3E");
  }

  .homework-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 2 2h12c1.11 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z'/%3E%3C/svg%3E");
  }

  .discussion-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v5l7-5h5c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H11l-4 3v-3H4V4h16v12z'/%3E%3C/svg%3E");
  }

  .group-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.998 1.998 0 0 0 18 7h-2c-.8 0-1.54.37-2.01 1.01l-.74 2.22-2.99-2.99C10.05 6.85 9.49 6.67 8.9 6.67c-1.66 0-3 1.34-3 3 0 .59.18 1.15.5 1.61L9 14.89V22h2v-6h2v6h4z'/%3E%3C/svg%3E");
  }

  .ai-assistant-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M20 9V7c0-1.1-.9-2-2-2h-3c0-1.66-1.34-3-3-3S9 3.34 9 5H6c-1.1 0-2 .9-2 2v2c-1.66 0-3 1.34-3 3s1.34 3 3 3v4c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-4c1.66 0 3-1.34 3-3s-1.34-3-3-3zm-2 10H6V7h12v12z'/%3E%3C/svg%3E");
  }

  .materials-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z'/%3E%3C/svg%3E");
  }

  .settings-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E");
  }

  /* 子标签图标样式 */
  .outline-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M3 9h14V7H3v2zm0 4h14v-2H3v2zm0 4h14v-2H3v2zm16 0h2v-2h-2v2zm0-10v2h2V7h-2zm0 6h2v-2h-2v2z'/%3E%3C/svg%3E");
  }

  .list-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z'/%3E%3C/svg%3E");
  }

  /* 子标签导航样式 */
  .sub-tabs {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    margin-bottom: 1.5rem;
  }

  .sub-tab-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    font-weight: 500;
  }

  .sub-tab-item:hover {
    color: var(--primary-color);
    background-color: rgba(66, 153, 225, 0.05);
  }

  .sub-tab-item.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: rgba(66, 153, 225, 0.1);
  }

  .sub-tab-icon {
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.7;
  }

  .sub-tab-item.active .sub-tab-icon {
    opacity: 1;
  }

  /* 子面板样式 */
  .sub-panel {
    flex: 1;
    overflow: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
  }

  .sub-panel::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .sub-panel::-webkit-scrollbar-track {
    background: transparent;
  }

  .sub-panel::-webkit-scrollbar-thumb {
    background: rgba(150, 150, 150, 0.3);
    border-radius: 4px;
  }

  .sub-panel::-webkit-scrollbar-thumb:hover {
    background: rgba(150, 150, 150, 0.5);
  }

  /* 嵌入式视图 */
  .embedded-view {
    width: 100%;
    height: 95vh;
    min-height: 500px;
    border: none;
    border-radius: var(--border-radius-md);
  }

  /* 返回按钮样式 */
  .back-button {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .back-button:hover {
    background-color: rgba(66, 153, 225, 0.1);
  }

  .back-button .back-icon {
    margin-right: 0.5rem;
  }

  /* 讨论和小组列表样式 */
  .discussion-list, .group-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow-y: auto;
  }

  .discussion-item, .group-item {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
  }

  .discussion-item:hover, .group-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .discussion-header, .group-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .discussion-title, .group-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    flex: 1;
  }

  .discussion-course, .group-members {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    background: #f3f4f6;
    color: #6b7280;
    margin-left: 1rem;
  }

  /* AI助手模块样式 */
  .ai-modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .module-card {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: 1px solid #f0f0f0;
    overflow: hidden;
    min-height: 280px;
  }

  .module-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border-color: #d1d5db;
  }

  .module-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 1.5rem 1rem;
    position: relative;
  }

  .module-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .ai-chat-icon {
    background: linear-gradient(135deg, #4F9CF9 0%, #3B82F6 100%);
  }

  .ai-tutor-icon {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  }

  .ai-practice-icon {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  }

  .module-icon i {
    font-size: 24px;
    color: white;
  }

  .icon-ai-chat::before {
    content: "🤖";
    font-style: normal;
  }

  .icon-ai-tutor::before {
    content: "🎓";
    font-style: normal;
  }

  .icon-ai-practice::before {
    content: "📝";
    font-style: normal;
  }

  .module-content {
    flex: 1;
    padding: 0 1.5rem 1rem;
    text-align: center;
  }

  .module-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
  }

  .module-desc {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.5;
    margin: 0;
  }

  .module-footer {
    padding: 1rem 1.5rem 1.5rem;
    border-top: 1px solid #f3f4f6;
    background: rgba(249, 250, 251, 0.5);
  }

  .module-btn {
    width: 100%;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn-blue {
    background: #3B82F6;
    color: white;
  }

  .btn-blue:hover {
    background: #2563EB;
  }

  .btn-green {
    background: #10B981;
    color: white;
  }

  .btn-green:hover {
    background: #059669;
  }

  .btn-orange {
    background: #F59E0B;
    color: white;
  }

  .btn-orange:hover {
    background: #D97706;
  }

  /* 学习资料网格样式 */
  .materials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
  }

  .material-category-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    text-align: center;
  }

  .material-category-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .category-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
  }

  .category-count {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .courses-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }

    .ai-modules-grid {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .content-layout {
      flex-direction: column;
    }

    .sidebar {
      width: 100%;
      height: auto;
      order: 2;
    }

    .main-content {
      order: 1;
    }

    .courses-grid {
      grid-template-columns: 1fr;
    }

    .homework-info {
      flex-direction: column;
      gap: 0.5rem;
    }

    .ai-modules-grid {
      grid-template-columns: 1fr;
    }

    .materials-grid {
      grid-template-columns: 1fr;
    }
  }
  </style>
