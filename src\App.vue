<template>
  <div id="app">
    <ErrorBoundary>
      <router-view />
    </ErrorBoundary>
    <NotificationToast />
    <!-- stagewise工具栏 -->
    <div id="stagewise-toolbar-container"></div>
  </div>
</template>

<script setup>
// 导入全局样式
import '@/assets/styles/variables.css';
import '@/assets/styles/theme.css';
import '@/assets/styles/global.css';
import '@/assets/styles/glassmorphism.css';

// 导入全局组件
import ErrorBoundary from '@/components/common/ErrorBoundary.vue';
import NotificationToast from '@/components/common/NotificationToast.vue';

import { onMounted, watch, nextTick } from 'vue';
import { createApp } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

// 监听路由变化，确保首页滚动到顶部
watch(
  () => route.path,
  (newPath) => {
    if (newPath === '/') {
      // 等待DOM更新后再滚动
      nextTick(() => {
        window.scrollTo(0, 0);
      });
    }
  }
);

// 在组件挂载后初始化stagewise工具栏
onMounted(() => {
  // 首次加载时，如果是首页则滚动到顶部
  if (route.path === '/') {
    // 立即滚动到顶部
    window.scrollTo(0, 0);
    
    // 确保在所有内容加载后再次滚动到顶部
    setTimeout(() => {
      window.scrollTo(0, 0);
    }, 100);
  }

  // 确保只在开发环境中加载
  if (import.meta.env.DEV || process.env.NODE_ENV === 'development') {
    // 动态导入Stagewise工具栏和Vue插件
    Promise.all([
      import('@stagewise/toolbar-vue'),
      import('@stagewise-plugins/vue')
    ]).then(([{ StagewiseToolbar }, VuePlugin]) => {
      // 创建工具栏实例
      const app = createApp(StagewiseToolbar, {
        config: {
          plugins: [VuePlugin.default || VuePlugin]
        }
      });
      
      // 挂载到指定容器
      app.mount('#stagewise-toolbar-container');
      
      console.log('Stagewise toolbar initialized with Vue plugin');
    }).catch(err => {
      console.error('Failed to load stagewise toolbar:', err);
    });
  }
});
</script>

<style>
/* 全局样式已通过import导入 */
</style>
