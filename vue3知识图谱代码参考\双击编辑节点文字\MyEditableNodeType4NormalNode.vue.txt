<template>
  <div
          :class="['my-node', (enableEditingMode && 'my-node-editable')]"
  >
    <input v-if="enableEditingMode && editing && checked" ref="$inputRef" :value="node.text" @blur="stopEditNodeText">
    <div v-else class="my-node-text" :title="enableEditingMode ? 'Double click to edit text' : undefined" :style="{color: node.fontColor}" @dblclick="startEditNodeText">
      <div>{{ node.text }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, defineEmits } from 'vue';
import {RelationGraphInstance, RGNode} from 'relation-graph-vue3';
const props = defineProps<{
  graphInstance: RelationGraphInstance,
  enableEditingMode: boolean,
  node: RGNode
}>();
const node = computed(() => {
    return props.node;
});
const $inputRef = ref<HTMLInputElement>();
const editing = ref(false);
const emit = defineEmits(['onNodeTextChange']);
const checked = computed(() => {
    return props.graphInstance.options.checkedNodeId === props.node.id;
});

const startEditNodeText = (e: MouseEvent) => {
    if (!props.enableEditingMode) return;
    editing.value = true;
    nextTick(() => {
        $inputRef.value!.focus();
    });
};

const stopEditNodeText = (e:any) => {
    const newTextValue = e.target.value;
    editing.value = false;
    emit('onNodeTextChange', newTextValue);
};
</script>

<style lang="scss" scoped>
.my-node {
  width: 100%;
  height: 100%;
  color: #000000;
  display: flex;
  place-items: center;
  justify-content: center;
  padding-left:5px;
  padding-right: 5px;
  box-sizing: border-box;
  input {
    background-color: rgba(35, 30, 37, 0.68);
    color: #ffffff;
    border-radius: 5px;
    width: 100px;
    padding-left:10px;
    padding-right:10px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    border-width: 0px;
    &:focus {
      background-color: rgba(166,41,239,0.5);
    }
  }
}
.my-node-editable {
  .my-node-text {
    cursor: pointer;
  }
}
</style>
