<template>
  <div class="outline-page">
    <div class="container">
      <div class="header-actions">
        <router-link to="/my?tab=outline" class="back-to-dashboard-btn">
          <span class="back-icon">←</span> 返回大纲列表
        </router-link>
        <router-link :to="`/graph/${courseId}`" class="enter-graph-btn">
          <span class="graph-icon">🔍</span> 进入知识图谱
        </router-link>
      </div>
      <OutlineToolbar v-if="false && !embedded && !isEditMode" />
      <div class="outline-content">
        <CourseSelector v-if="false && !onlyOutline && !isEditMode" />
        <div v-if="courseId" class="outline-tree">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-state">
            <div class="spinner"></div>
            <p>正在加载知识图谱节点...</p>
          </div>
          
          <!-- 错误状态 -->
          <div v-else-if="error" class="error-state">
            <p>{{ error }}</p>
            <button @click="fetchGraphNodes" class="retry-btn">重试</button>
          </div>
          
          <!-- 节点树 -->
          <div v-else-if="hierarchicalNodes.length > 0" class="node-container">
            <div class="outline-header">
              <h2 v-if="isLoading && !currentGraph">加载大纲信息...</h2>
              <h2 v-else>{{ currentGraph ? currentGraph.name : `知识图谱大纲 #${courseId}` }}</h2>
              <div class="search-box">
                <input type="text" v-model="searchQuery" placeholder="搜索知识点..." @input="searchNodes" />
                <button v-if="searchQuery" class="clear-search" @click="clearSearch">×</button>
                <button class="search-button" @click="searchNodes">搜索</button>
              </div>
            </div>
            
            <!-- 节点列表 -->
            <div class="node-tree">
              <OutlineNodeItem
                v-for="node in hierarchicalNodes"
                :key="node.id"
                :node="node"
                :level="1"
                :editable="isEditMode"
                @toggle="toggleNode"
                @edit="editNode"
                @add-child="addChildNode"
                @delete="deleteNodeEvent"
              />
            </div>
            
            <!-- 空状态 -->
            <div v-if="hierarchicalNodes.length === 0" class="empty-state">
              <p>此知识图谱暂无节点</p>
              <button v-if="isEditMode" class="add-root-node-btn" @click="addRootNode">添加根节点</button>
            </div>
          </div>
          
          <!-- 无节点状态 -->
          <div v-else class="empty-state">
            <p>此知识图谱暂无节点</p>
            <button v-if="isEditMode" class="add-root-node-btn" @click="addRootNode">添加根节点</button>
          </div>
        </div>
        <OutlineList v-else @select-outline="handleOutlineSelect" />
      </div>
    </div>
    
    <!-- 节点编辑对话框 -->
    <div v-if="showEditDialog" class="node-dialog-overlay" @click.self="cancelEdit">
      <div class="node-dialog">
        <h3>{{ editingNode.id ? '修改节点' : '添加节点' }}</h3>
        <form >
          <div class="form-group">
            <label for="nodeName">节点名称</label>
            <input type="text" id="nodeName" v-model="editingNode.name" required>
          </div>
          <div class="form-group">
            <label for="nodeContent">节点内容</label>
            <textarea id="nodeContent" v-model="editingNode.content" rows="4"></textarea>
          </div>
          <div class="dialog-actions">
            <button type="button" class="cancel-btn" @click="cancelEdit">取消</button>
            <button type="submit" class="save-btn" @click="saveNode">保存</button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- 确认删除对话框 -->
    <div v-if="showDeleteDialog" class="node-dialog-overlay" @click.self="cancelDelete">
      <div class="node-dialog delete-dialog">
        <h3>确认删除</h3>
        <p>您确定要删除节点 "{{ deletingNode.name }}" 及其所有子节点吗？</p>
        <div class="dialog-actions">
          <button type="button" class="cancel-btn" @click="cancelDelete">取消</button>
          <button type="button" class="delete-confirm-btn" @click="confirmDelete">删除</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import OutlineToolbar from '@/components/outline/OutlineToolbar.vue';
import OutlineList from '@/components/outline/OutlineList.vue';
import CourseSelector from '@/components/outline/CourseSelector.vue';
import OutlineNodeItem from '@/components/outline/OutlineNodeItem.vue';
import { getKnowledgeGraphNodes, createNode, updateNode, deleteNode, getKnowledgeGraphList } from '@/api/graph';

const route = useRoute();
const router = useRouter();

// Check if we're in edit mode
const isEditMode = computed(() => route.meta.isEditMode || false);

// 提取URL参数
const embedded = ref(route.query.embedded === 'true');
const onlyOutline = ref(route.query.onlyOutline === 'true');
const courseId = ref(route.params.id || route.query.courseId || null);

// 数据状态
const isLoading = ref(false);
const error = ref(null);
//节点列表
const graphNodes = ref([]);
//展开的节点
const expandedNodes = ref([]);
//当前知识图谱
const currentGraph = ref(null);
const hierarchicalData = ref([]); // 保存构建好的层级数据结构
// 搜索相关
const searchQuery = ref('');
const filteredNodes = ref([]);

// 节点操作状态
const showEditDialog = ref(false);
const showDeleteDialog = ref(false);
const editingNode = ref({});
const deletingNode = ref({});
const isNewNode = ref(false);
const parentNodeId = ref(null);

// 构建节点的层级结构
const buildHierarchy = () => {
  if (!graphNodes.value || graphNodes.value.length === 0) {
    hierarchicalData.value = [];
    return;
  }
  
  // 获取要显示的节点 - 如果有搜索查询，只显示过滤后的节点及其父节点
  let nodesToDisplay = graphNodes.value;
  
  if (searchQuery.value && filteredNodes.value.length > 0) {
    // 创建一个存储需要显示的节点ID的集合
    const nodeIdsToShow = new Set();
    
    // 添加所有匹配的节点ID
    filteredNodes.value.forEach(node => {
      nodeIdsToShow.add(node.id);
      
      // 添加所有父节点ID以保持树形结构
      let parentId = node.parentId;
      while (parentId) {
        nodeIdsToShow.add(parentId);
        const parentNode = graphNodes.value.find(n => n.id === parentId);
        parentId = parentNode ? parentNode.parentId : null;
      }
    });
    
    // 只显示匹配节点及其父节点
    nodesToDisplay = graphNodes.value.filter(node => nodeIdsToShow.has(node.id));
  }
  
  // 首先找出根节点（没有parent_id的节点）
  const rootNodes = nodesToDisplay.filter(node => 
    node.parentId === null || 
    node.parentId === undefined
  );
  
  // 构建层级结构
  hierarchicalData.value = rootNodes.map(node => {
    return {
      ...node,
      expanded: expandedNodes.value.includes(node.id),
      children: getNodeChildren(node.id, nodesToDisplay),
      isSearchResult: filteredNodes.value.some(n => n.id === node.id)
    };
  });
  
  // 调试输出
  console.log('构建的层级结构:', hierarchicalData.value);
};

// 获取节点的所有子节点（递归构建层级）
const getNodeChildren = (nodeId, nodesToDisplay) => {
  const children = nodesToDisplay.filter(node => node.parentId === nodeId);
  
  // 递归获取每个子节点的子节点
  return children.map(child => {
    return {
      ...child,
      expanded: expandedNodes.value.includes(child.id),
      children: getNodeChildren(child.id, nodesToDisplay),
      isSearchResult: filteredNodes.value.some(n => n.id === child.id)
    };
  });
};

// 根据层级结构获取顶级节点（根节点）
const hierarchicalNodes = computed(() => {
  return hierarchicalData.value;
});

// 切换节点展开/折叠状态
const toggleNode = (nodeId) => {
  const index = expandedNodes.value.indexOf(nodeId);
  if (index > -1) {
    expandedNodes.value.splice(index, 1);
  } else {
    expandedNodes.value.push(nodeId);
  }
  
  // 重新构建层级结构以更新expanded状态
  buildHierarchy();
};

// 获取知识图谱节点数据
const fetchGraphNodes = async () => {
  if (!courseId.value) return;
  
  isLoading.value = true;
  error.value = null;
  
  try {
    // 获取节点数据
    const nodes = await getKnowledgeGraphNodes(courseId.value);
    graphNodes.value = Array.isArray(nodes) ? nodes : [];
    console.log('原始节点数据:', graphNodes.value);
    
    // 默认展开所有一级节点
    expandedNodes.value = graphNodes.value
      .filter(node => !node.parentId)
      .map(node => node.id);
    
    // 构建层级结构
    buildHierarchy();
    
  } catch (err) {
    console.error('获取知识图谱数据失败:', err);
    error.value = '获取知识图谱数据失败，请重试';
  } finally {
    isLoading.value = false;
  }
};

// 处理选择大纲的事件
const handleOutlineSelect = (course) => {
  if (course && course.id) {
    courseId.value = course.id;
    currentGraph.value = course;
    // 更新URL，保留其他参数
    router.push({
      query: { ...route.query, courseId: course.id }
    });
    
    // 加载节点数据
    fetchGraphNodes();
  }
};

// 编辑节点
const editNode = (node) => {
  editingNode.value = { ...node };
  isNewNode.value = false;
  showEditDialog.value = true;
};

// 添加子节点
const addChildNode = (node) => {
  editingNode.value = { 
    name: '',
    content: '',
    parentId: node.id,
    graphId:courseId.value
  };
  isNewNode.value = true;
  parentNodeId.value = node.id;
  showEditDialog.value = true;
};

// 添加根节点
const addRootNode = () => {
  editingNode.value = { 
    name: '',
    content: '',
    parentId: null,
    graphId: courseId.value
  };
  isNewNode.value = true;
  parentNodeId.value = null;
  showEditDialog.value = true;
};

// 保存节点（新建或编辑）
const saveNode = async () => {
  try {
    if (!editingNode.value.name) {
      alert('节点名称不能为空');
      return;
    }
    
    isLoading.value = true;
    let result;
    
    if (isNewNode.value) {
      // 创建新节点
      result = await createNode({
        name: editingNode.value.name,
        content: editingNode.value.content,
        parentId: editingNode.value.parentId,
        graphId:courseId.value
      });
      
      console.log('创建节点成功:', result);
    } else {
      // 更新节点
      result = await updateNode( {
        name: editingNode.value.name,
        content: editingNode.value.content,
        id:editingNode.value.id,
        graphId:courseId.value
        // parentId:parentNodeId.value.parentId
      });
      
      console.log('更新节点成功:', result);
    }
    
    // 重新获取节点数据
    // await fetchGraphNodes();
    // showEditDialog.value = false;
    
  } catch (err) {
    console.error('保存节点失败:', err);
    alert('保存失败，请重试');
  } finally {
    isLoading.value = false;
  }
};

// 取消编辑
const cancelEdit = () => {
  showEditDialog.value = false;
  editingNode.value = {};
};

// 删除节点确认
const deleteNodeEvent = (node) => {
  deletingNode.value = node;
  showDeleteDialog.value = true;
};

// 取消删除
const cancelDelete = () => {
  showDeleteDialog.value = false;
  deletingNode.value = {};
};

// 确认删除
const confirmDelete = async () => {
  try {
    isLoading.value = true;
    
    // 删除节点
    await deleteNode(deletingNode.value.id);
    console.log('删除节点成功:', deletingNode.value.id);
    
    // 重新获取节点数据
    await fetchGraphNodes();
    showDeleteDialog.value = false;
    
  } catch (err) {
    console.error('删除节点失败:', err);
    alert('删除失败，请重试');
  } finally {
    isLoading.value = false;
  }
};

// 搜索节点
const searchNodes = () => {
  // 如果搜索框为空，重置过滤
  if (!searchQuery.value) {
    filteredNodes.value = [];
    buildHierarchy();
    return;
  }
  
  // 根据名称或内容搜索节点
  filteredNodes.value = graphNodes.value.filter(node => {
    return node.name?.toLowerCase().includes(searchQuery.value.toLowerCase()) || 
           node.content?.toLowerCase().includes(searchQuery.value.toLowerCase());
  });
  
  // 展开包含搜索结果的所有父节点
  if (filteredNodes.value.length > 0) {
    const nodesToExpand = new Set();
    
    filteredNodes.value.forEach(node => {
      // 将节点ID添加到需要展开的集合中
      nodesToExpand.add(node.id);
      
      // 递归添加所有父节点
      let parentId = node.parentId;
      while (parentId) {
        nodesToExpand.add(parentId);
        const parentNode = graphNodes.value.find(n => n.id === parentId);
        parentId = parentNode ? parentNode.parentId : null;
      }
    });
    
    // 更新展开的节点列表
    expandedNodes.value = [...nodesToExpand];
  }
  
  // 重建层级结构以反映搜索结果
  buildHierarchy();
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = '';
  filteredNodes.value = [];
  buildHierarchy();
};

onMounted(() => {
  console.log('OutlinePage mounted with params:', {
    embedded: embedded.value,
    onlyOutline: onlyOutline.value,
    courseId: courseId.value,
    isEditMode: isEditMode.value
  });
  
  // 如果有courseId，加载节点数据
  if (courseId.value) {
    // 总是获取课程信息，确保标题显示正确
    fetchCourseInfo();
    fetchGraphNodes();
  }
});

// 获取课程信息
const fetchCourseInfo = async () => {
  try {
    const response = await getKnowledgeGraphList();
    if (response && response.rows && Array.isArray(response.rows)) {
      const foundCourse = response.rows.find(course => course.id == courseId.value);
      if (foundCourse) {
        // Normalize the course data to ensure we have a name property
        currentGraph.value = {
          ...foundCourse,
          name: foundCourse.name || foundCourse.title || foundCourse.courseName || `知识图谱大纲 #${courseId.value}`
        };
        console.log('获取到课程信息:', currentGraph.value);
      }
    }
  } catch (error) {
    console.error('获取课程信息失败:', error);
  }
};
</script>

<style scoped>
.outline-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.header-actions {
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: flex-start;
}

.back-to-dashboard-btn {
  display: flex;
  align-items: center;
  color: #4a89dc;
  font-weight: 500;
  text-decoration: none;
  padding: 10px 16px;
  border-radius: 6px;
  background-color: #f5f7fa;
  transition: all 0.2s ease;
  width: fit-content;
  box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.back-to-dashboard-btn:hover {
  background-color: #e9eef8;
  color: #3a79cc;
  box-shadow: 0 2px 5px rgba(0,0,0,0.15);
  transform: translateY(-1px);
}

.back-icon {
  font-size: 18px;
  margin-right: 8px;
}

.enter-graph-btn {
  display: flex;
  align-items: center;
  color: #4a89dc;
  font-weight: 500;
  text-decoration: none;
  padding: 10px 16px;
  border-radius: 6px;
  background-color: #f5f7fa;
  transition: all 0.2s ease;
  width: fit-content;
  box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.enter-graph-btn:hover {
  background-color: #e9eef8;
  color: #3a79cc;
  box-shadow: 0 2px 5px rgba(0,0,0,0.15);
  transform: translateY(-1px);
}

.graph-icon {
  font-size: 18px;
  margin-right: 8px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.outline-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 35px;
  flex: 1;
}

.outline-tree {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  flex: 1;
  overflow: auto;
}

.outline-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.outline-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
  padding: 4px 8px;
  max-width: 300px;
}

.search-box input {
  border: none;
  outline: none;
  padding: 6px;
  width: 180px;
  font-size: 14px;
}

.search-button {
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 14px;
}

.search-button:hover {
  background-color: #3a5ce5;
}

.clear-search {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 18px;
  padding: 0 6px;
}

.node-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.node-tree {
  flex: 1;
  overflow-y: auto;
}

.node-item {
  margin-bottom: 0.5rem;
}

.node-header {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.node-header:hover {
  background-color: #f5f5f5;
}

.node-toggle {
  margin-right: 0.5rem;
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
  color: #666;
}

.node-title {
  font-weight: 500;
  color: #333;
  flex-grow: 1;
}

.node-content {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  padding-left: 2rem;
}

.node-marker {
  margin-right: 0.5rem;
  color: #666;
}

.node-children {
  padding-left: 1rem;
  margin-top: 0.25rem;
}

.level-1 > .node-header {
  background-color: rgba(0, 0, 0, 0.03);
}

.level-2 .node-title {
  font-weight: normal;
}

.level-3 .node-title,
.level-4 .node-title,
.level-5 .node-title {
  font-weight: normal;
  font-size: 0.95em;
}

.level-3 {
  margin-left: 0.5rem;
}

.level-4 {
  margin-left: 1rem;
}

.level-5,
.level-n {
  margin-left: 1.5rem;
}

.empty-children {
  padding: 0.5rem 0.5rem 0.5rem 2rem;
  color: #999;
  font-style: italic;
  font-size: 0.9rem;
}

/* 不同层级的节点标记符 */
.level-2 .node-marker {
  content: "•";
}

.level-3 .node-marker {
  content: "◦";
}

.level-4 .node-marker,
.level-5 .node-marker,
.level-n .node-marker {
  content: "▪";
  font-size: 0.8em;
}

/* 状态样式 */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  gap: 1rem;
  color: #666;
}

.error-state {
  color: #f56c6c;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #409eff;
  animation: spin 0.8s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.retry-btn {
  padding: 0.5rem 1.25rem;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  margin-top: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background-color: #337ecc;
}

@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }
  
  .outline-tree {
    padding: 1rem;
  }
}

.node-content-block {
  padding: 0.5rem 1rem 1rem 2.5rem;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #555;
  border-left: 2px solid #eee;
  margin-left: 0.5rem;
  white-space: pre-line;
  max-height: 150px;
  overflow-y: auto;
  background-color: rgba(0, 0, 0, 0.01);
}

.child-content {
  padding-left: 3rem;
  margin-left: 1.5rem;
  background-color: rgba(0, 0, 0, 0.01);
}

/* 节点操作按钮样式 */
.node-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.node-header:hover .node-actions,
.node-content:hover .node-actions {
  opacity: 1;
}

.action-btn {
  padding: 0.25rem 0.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.2s ease;
}

.edit-btn {
  background-color: #ecf5ff;
  color: #409eff;
}

.edit-btn:hover {
  background-color: #d9ecff;
}

.add-btn {
  background-color: #f0f9eb;
  color: #67c23a;
}

.add-btn:hover {
  background-color: #e1f3d8;
}

.delete-btn {
  background-color: #fef0f0;
  color: #f56c6c;
}

.delete-btn:hover {
  background-color: #fde2e2;
}

/* 对话框样式 */
.node-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.node-dialog {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.node-dialog h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  color: #333;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #409eff;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancel-btn, 
.save-btn, 
.delete-confirm-btn {
  padding: 0.5rem 1.25rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background-color: #f5f7fa;
  color: #606266;
}

.cancel-btn:hover {
  background-color: #e4e7ed;
}

.save-btn {
  background-color: #409eff;
  color: white;
}

.save-btn:hover {
  background-color: #337ecc;
}

.delete-confirm-btn {
  background-color: #f56c6c;
  color: white;
}

.delete-confirm-btn:hover {
  background-color: #e74c3c;
}

.delete-dialog {
  max-width: 400px;
}

.delete-dialog p {
  margin-bottom: 1.5rem;
  color: #606266;
}

.edit-mode-indicator {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.edit-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  max-width: 300px;
  margin-left: auto;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 0 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-icon {
  margin-right: 8px;
  color: #8e8e93;
  font-size: 14px;
}

.search-box input {
  width: 180px;
  padding: 8px 0;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  background-color: transparent;
}

.search-box input:focus {
  outline: none;
}

.search-button {
  background-color: #0071e3;
  color: white;
  border: none;
  border-radius: 15px;
  padding: 4px 12px;
  font-size: 12px;
  margin-left: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-button:hover {
  background-color: #0077ed;
}

.clear-search {
  position: static;
  background: none;
  border: none;
  color: #8e8e93;
  cursor: pointer;
  padding: 0 5px;
  font-size: 16px;
  transform: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.clear-search:hover {
  color: #555;
  background-color: transparent;
}

/* 搜索结果高亮样式 */
.search-result {
  background-color: rgba(64, 158, 255, 0.1);
  border-left: 3px solid #409eff;
}

.edit-badge {
  background-color: #409eff;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-right: 0.5rem;
}

.add-root-node-btn {
  padding: 0.5rem 1.25rem;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-root-node-btn:hover {
  background-color: #337ecc;
}
</style> 