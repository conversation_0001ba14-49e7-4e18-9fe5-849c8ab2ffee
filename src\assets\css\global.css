/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-body);
  font-size: 16px;
  line-height: 1.6;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
  position: relative;
}

/* 科技网格背景 - 更微妙的视觉效果 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(to right, var(--grid-color) 1px, transparent 1px),
    linear-gradient(to bottom, var(--grid-color) 1px, transparent 1px);
  background-size: 40px 40px;
  opacity: 0.3; /* 减少视觉干扰 */
  pointer-events: none;
  z-index: -1;
}

*, *::before, *::after {
  box-sizing: border-box;
}

/* 标题样式 - 优化层次结构 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
  color: var(--text-color); /* 确保在暗模式下有足够对比度 */
}

h1 {
  font-size: 2.25rem;
  margin-bottom: 1.5rem;
  position: relative;
}

h2 {
  font-size: 1.875rem;
  margin-top: 1.5rem; /* 增加空间以提高视觉层次 */
}

h3 {
  font-size: 1.5rem;
  margin-top: 1.25rem;
}

h4 {
  font-size: 1.25rem;
  margin-top: 1rem;
}

/* 科技感下划线 - 使层次更明显 */
h1::after {
  content: '';
  display: block;
  width: 80px; /* 更宽的标识线 */
  height: 4px; /* 更粗的线条 */
  background: linear-gradient(90deg, var(--primary-color), transparent);
  margin-top: 0.75rem;
}

h2::after {
  content: '';
  display: block;
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), transparent);
  margin-top: 0.5rem;
}

/* 容器 - 通过响应式类定义 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* 减少首页信息密度 */
.homepage-section {
  margin-bottom: var(--spacing-xxl);
  padding: var(--spacing-lg) 0;
}

.section-title {
  font-size: 1.75rem;
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.section-description {
  max-width: 800px;
  margin: 0 auto var(--spacing-xl);
  text-align: center;
  color: var(--text-light);
  font-size: 1.1rem;
}

/* 卡片样式 - 科技感优化 */
.card {
  background-color: var(--background-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-lg); /* 增加卡片间距，减少密度 */
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-3px); /* 轻微上浮效果增强交互感 */
  border-color: var(--primary-color);
}

/* 科技感高光边框 */
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  opacity: 0.8;
}

/* 卡片内部内容间距优化 */
.card-title {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-color);
}

.card-content {
  color: var(--text-light);
}

/* 卡片重要性级别 */
.card-primary::before {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-light));
  opacity: 1;
  height: 4px;
}

.card-secondary::before {
  background: linear-gradient(90deg, var(--secondary-color), var(--secondary-color-light));
}

.card-accent::before {
  background: linear-gradient(90deg, var(--accent-color), var(--accent-color-light));
}

/* 布局工具类 */
.flex {
  display: flex;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 间距工具类 */
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }

.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }

.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }

/* 按钮样式 - 提高可见性和交互性 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 1.25rem;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  border: none;
  letter-spacing: 0.01em;
  position: relative;
  overflow: hidden;
}

/* 全局下拉菜单项样式 - 确保背景不透明 */
.dropdown-item {
  background-color: #FFFFFF !important;
  opacity: 1 !important;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 0 15px var(--glow-color);
}

.btn-primary:hover {
  background-color: var(--primary-color-dark);
  transform: translateY(-2px);
  box-shadow: 0 0 20px var(--glow-color);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.btn-outline:hover {
  background-color: var(--highlight-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-icon {
  padding: 0.5rem;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon svg {
  width: 1.25rem;
  height: 1.25rem;
}

/* 改进的主题切换动画 */
body {
  transition: background-color 0.5s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 暗色主题下的优化样式 */
[data-theme="dark"] img {
  filter: brightness(0.9);
}

[data-theme="dark"] .btn-outline {
  border-color: var(--border-dark);
}

[data-theme="dark"] .card {
  background-color: var(--background-light);
}

/* 滚动条样式 - 改进对比度和可视性 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(150, 150, 150, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(150, 150, 150, 0.5);
}

/* Firefox滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
}

/* IE滚动条样式 */
body {
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

/* 链接样式 - 提高可识别性 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-normal);
  position: relative;
  font-weight: 500;
}

a:hover {
  color: var(--primary-color-dark);
  text-decoration: underline;
}

/* 高对比度模式链接增强 */
[data-theme="dark"] a {
  color: var(--primary-color-light);
}

[data-theme="dark"] a:hover {
  color: var(--primary-color);
}

/* 表格样式 - 学术风格增强 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: var(--spacing-md) 0;
  font-size: 0.9rem;
  box-shadow: var(--shadow-sm);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

th {
  background-color: var(--background-light);
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-color);
  border-bottom: 2px solid var(--border-color);
}

td {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-light);
}

tr:hover {
  background-color: var(--highlight-color);
}

/* 暗色模式表格优化 */
[data-theme="dark"] th {
  background-color: var(--background-dark);
  border-bottom-color: var(--border-dark);
}

[data-theme="dark"] td {
  border-bottom-color: var(--border-dark);
}

/* 强调文本 */
.text-primary { color: var(--primary-color) !important; }
.text-accent { color: var(--accent-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }

/* 减少视觉噪音的提示组件 */
.alert {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-lg);
  border-left: 4px solid transparent;
}

.alert-info {
  background-color: rgba(var(--info-color-rgb), 0.1);
  border-left-color: var(--info-color);
  color: var(--text-color);
}

.alert-success {
  background-color: rgba(var(--success-color-rgb), 0.1);
  border-left-color: var(--success-color);
  color: var(--text-color);
}

.alert-warning {
  background-color: rgba(var(--warning-color-rgb), 0.1);
  border-left-color: var(--warning-color);
  color: var(--text-color);
}

.alert-danger {
  background-color: rgba(var(--danger-color-rgb), 0.1);
  border-left-color: var(--danger-color);
  color: var(--text-color);
} 