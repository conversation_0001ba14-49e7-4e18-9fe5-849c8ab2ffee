# 教案修改功能实现文档

## 概述

本文档描述了HiProf教育平台中教案修改功能的完整实现，包括API修复、前端页面开发、路由配置等。

## 功能特性

### ✅ 已实现功能

1. **API接口修复**
   - 修复了 `updateLessonPlan` 函数的URL和参数
   - 符合后端接口文档规范（PUT /tp/plan）
   - 正确处理包含ID的完整TpPlan对象

2. **教案编辑页面**
   - 创建了完整的编辑页面 `LessonEditPage.vue`
   - 支持加载现有教案数据
   - 表单验证和错误处理
   - 响应式设计，适配移动端

3. **路由配置**
   - 添加了编辑路由：`/teacher/lesson-edit/:id`
   - 支持通过URL参数传递教案ID

4. **列表页面集成**
   - 修改了列表页面的编辑按钮功能
   - 点击编辑按钮跳转到编辑页面

## 技术实现

### API修复详情

**修复前：**
```javascript
export const updateLessonPlan = (planId, planData) => {
  return request({
    url: `/tp/plan/${planId}`,  // ❌ 错误的URL
    method: 'put',
    data: planData
  });
};
```

**修复后：**
```javascript
export const updateLessonPlan = (planData) => {
  return request({
    url: '/tp/plan',  // ✅ 正确的URL
    method: 'put',
    data: planData    // planData必须包含id字段
  });
};
```

### 数据结构处理

编辑页面正确处理了以下数据结构：

1. **保留原有字段**
   - `id`: 教案ID
   - `createBy`: 创建者
   - `createTime`: 创建时间
   - `createUser`: 创建用户ID

2. **更新审计字段**
   - `updateBy`: 更新者
   - `updateTime`: 更新时间

3. **嵌套结构处理**
   - `tpModuleList`: 教学模块列表
   - `content`: 模块内容对象

### 页面组件结构

```
LessonEditPage.vue
├── 页面头部（标题和描述）
├── 加载状态显示
├── 教案表单
│   ├── 基本信息（标题）
│   └── 教学模块
│       ├── 模块标题
│       ├── 模块内容
│       └── 附件链接
├── 表单操作按钮
└── 成功提示模态框
```

## 使用流程

1. **进入编辑页面**
   ```
   用户在教案列表页面点击"编辑"按钮
   ↓
   跳转到 /teacher/lesson-edit/{id}
   ↓
   页面加载现有教案数据
   ```

2. **编辑教案内容**
   ```
   修改教案标题
   ↓
   添加/删除/修改教学模块
   ↓
   填写模块标题、内容、附件链接
   ```

3. **提交更新**
   ```
   点击"更新教案"按钮
   ↓
   表单验证
   ↓
   调用 PUT /tp/plan API
   ↓
   显示成功提示
   ↓
   返回教案列表页面
   ```

## 文件清单

### 新增文件
- `src/pages/teacher/LessonEditPage.vue` - 教案编辑页面
- `src/test/lesson-edit-test.js` - 测试用例
- `test-lesson-edit.html` - 功能测试页面
- `LESSON_EDIT_FEATURE.md` - 本文档

### 修改文件
- `src/api/lessonPlan.js` - 修复updateLessonPlan函数
- `src/router/routes.js` - 添加编辑路由
- `src/pages/teacher/LessonListPage.vue` - 完善编辑功能

## 测试建议

### 基本功能测试
1. 从列表页面点击编辑按钮
2. 验证数据是否正确加载到表单
3. 修改教案标题和模块内容
4. 提交更新并验证结果

### 边界情况测试
1. 空标题提交（应显示验证错误）
2. 空模块内容提交（应显示验证错误）
3. 网络错误情况（应显示友好错误提示）
4. 无效的教案ID（应跳转回列表页面）

### 用户体验测试
1. 移动端响应式布局
2. 加载状态显示
3. 错误提示友好性
4. 操作流程顺畅性

## 开发环境测试

1. 启动开发服务器：
   ```bash
   npm run dev
   ```

2. 访问测试页面：
   - 教案列表：http://localhost:5174/teacher/lesson-list
   - 功能测试：file:///path/to/test-lesson-edit.html

3. 在浏览器控制台运行测试：
   ```javascript
   // 加载测试模块
   import('./src/test/lesson-edit-test.js').then(module => {
     window.testModule = module;
     // 运行测试
     module.testLessonEdit();
   });
   ```

## 重要修复

### 日期格式问题修复

**问题描述：**
- 后端期望日期格式：`yyyy-MM-dd HH:mm:ss`
- 前端发送格式：`2025-07-09T14:20:30.955Z` (ISO 8601)
- 错误信息：`Cannot deserialize value of type java.util.Date from String`

**解决方案：**
创建了日期格式化函数 `formatDateForBackend`：

```javascript
const formatDateForBackend = (date) => {
  if (!date) return ''

  // 如果已经是正确格式的字符串，直接返回
  if (typeof date === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(date)) {
    return date
  }

  // 转换为 yyyy-MM-dd HH:mm:ss 格式
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
```

**应用位置：**
- 教案级别：`createTime`, `updateTime`
- 模块级别：`createTime`, `updateTime`
- 内容级别：`createTime`, `updateTime`

## 注意事项

1. **后端依赖**
   - 确保后端API `PUT /tp/plan` 正常工作
   - 确保后端支持完整的TpPlan数据结构
   - 确保后端接受 `yyyy-MM-dd HH:mm:ss` 格式的日期

2. **数据完整性**
   - 编辑时保留所有原有的审计字段
   - 新增模块的ID设为0，由后端分配
   - 所有日期字段使用统一的格式化函数

3. **错误处理**
   - 网络错误显示友好提示
   - 表单验证错误实时显示
   - API错误根据状态码显示相应消息
   - 日期格式错误的预防和处理

4. **性能考虑**
   - 大型教案的加载性能
   - 模块数量较多时的渲染性能
   - 日期格式化的性能优化

## 后续优化建议

1. **功能增强**
   - 添加自动保存功能
   - 支持富文本编辑器
   - 添加文件上传功能
   - 支持教案模板

2. **用户体验**
   - 添加编辑历史记录
   - 支持协作编辑
   - 添加快捷键支持
   - 优化移动端体验

3. **技术优化**
   - 使用TypeScript增强类型安全
   - 添加单元测试覆盖
   - 优化组件性能
   - 添加国际化支持

## 总结

教案修改功能已完整实现，包括：
- ✅ API接口修复
- ✅ 编辑页面开发
- ✅ 路由配置
- ✅ 列表页面集成
- ✅ 表单验证
- ✅ 错误处理
- ✅ 响应式设计
- ✅ 测试用例

功能已准备就绪，可以进行测试和部署。
