<template>
  <div
    :class="['my-container-node', (enableEditingMode && 'my-node-editable')]"
    @mouseup="onMouseUp"
  >
    <div class="c-group-name">
      <input v-if="enableEditingMode && editing && checked" ref="$inputRef" :value="node.text" @blur="stopEditNodeText">
      <div v-else class="my-node-text" :title="enableEditingMode ? 'Double click to edit text' : undefined" @dblclick="startEditNodeText">
        <div>{{ node.text }}</div>
      </div>
    </div>
    <div style="text-align: left;width: 100%;">容器内节点数量：{{node.data!.containerNodes.length}}</div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, defineEmits } from 'vue';
import {RelationGraphInstance, RGNode} from 'relation-graph-vue3';
const props = defineProps<{
  graphInstance: RelationGraphInstance,
  enableEditingMode: boolean,
  node: RGNode
}>();
const node = computed(() => {
    return props.node;
});
const checked = computed(() => {
    return props.graphInstance.options.checkedNodeId === props.node.id;
});
const $inputRef = ref<HTMLInputElement>();
const editing = ref(false);
const emit = defineEmits(['onNodeTextChange', 'onNewNodeDropOnContainerNode']);

const startEditNodeText = (e: MouseEvent) => {
    if (!props.enableEditingMode) return;
    editing.value = true;
    nextTick(() => {
        $inputRef.value!.focus();
    });
};


const onMouseUp = () => {
    if (props.graphInstance.options.creatingNodePlot) {
        console.log('creatingNodePlot:new node drop on node container:', node.value.text);
        emit('onNewNodeDropOnContainerNode', node.value);
    }
};

const stopEditNodeText = (e:any) => {
    const newTextValue = e.target.value;
    editing.value = false;
    emit('onNodeTextChange', newTextValue);
};
</script>

<style lang="scss" scoped>
.my-container-node {
  width: 100%;
  height: 100%;
  color: #000000;
  box-sizing: border-box;
  position: relative;
  .c-group-name {
    position: absolute;
    left:0px;
    top:-40px;
    pointer-events: all;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    background-color: rgba(166,41,239,0.5);
    width: 150px;
    height: 40px;
    display: flex;
    place-items: center;
    justify-content: center;
    color: rgba(166,41,239,1);
  }
  input {
    background-color: rgba(35, 30, 37, 0.68);
    color: #ffffff;
    border-radius: 5px;
    width: 100px;
    height: 35px;
    padding-left:10px;
    padding-right:10px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    border-width: 0px;
    &:focus {
      background-color: rgba(166,41,239,0.5);
    }
  }
}
.my-node-editable {
  .my-node-text {
    cursor: pointer;
  }
}
</style>
