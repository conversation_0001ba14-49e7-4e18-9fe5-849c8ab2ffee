/* 暗色模式专用样式 - 确保所有元素在暗色模式下有适当的对比度 */

/* 增强暗色模式体验的额外样式 */
[data-theme="dark"] {
  /* 文本对比度增强 */
  --high-contrast-text: #ffffff;
  --text-on-primary: rgba(255, 255, 255, 0.95);
  
  /* 特定元素样式 */
  color-scheme: dark; /* 浏览器原生暗色模式支持 */
}

/* 暗色模式下增强对比度的元素 */
[data-theme="dark"] .btn-primary {
  color: var(--high-contrast-text);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 增强文字可读性 */
}

[data-theme="dark"] .card {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25); /* 更强的阴影效果 */
}

/* 暗色模式下提高表单元素的对比度 */
[data-theme="dark"] input, 
[data-theme="dark"] select, 
[data-theme="dark"] textarea {
  background-color: var(--background-dark);
  border-color: var(--border-dark);
  color: var(--text-color);
}

[data-theme="dark"] input:focus, 
[data-theme="dark"] select:focus, 
[data-theme="dark"] textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.3);
}

/* 暗色模式下提高图标对比度 */
[data-theme="dark"] .icon {
  filter: brightness(1.2); /* 使图标更亮 */
}

/* 暗色模式下的代码和预格式化文本 */
[data-theme="dark"] code, 
[data-theme="dark"] pre {
  background-color: rgba(0, 0, 0, 0.2);
  border-color: var(--border-dark);
}

/* 暗色模式下的引用 */
[data-theme="dark"] blockquote {
  border-left-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

/* 暗色模式下的图表颜色增强 */
[data-theme="dark"] .chart-area {
  background-color: var(--background-dark);
  border-color: var(--border-color);
}

/* 暗色模式下的表格增强 */
[data-theme="dark"] table {
  border-color: var(--border-dark);
}

[data-theme="dark"] thead {
  background-color: var(--background-dark);
}

[data-theme="dark"] tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.03); /* 轻微的条纹效果 */
}

/* 暗色模式下的导航栏 */
[data-theme="dark"] .navbar {
  background-color: var(--background-dark);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* 暗色模式下的下拉菜单 */
[data-theme="dark"] .dropdown-menu {
  background-color: var(--background-dark);
  border-color: var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 暗色模式下的提示信息 */
[data-theme="dark"] .alert {
  border-color: var(--border-dark);
}

/* 暗色模式下的模态框 */
[data-theme="dark"] .modal-content {
  background-color: var(--background-light);
  border-color: var(--border-dark);
}

[data-theme="dark"] .modal-header,
[data-theme="dark"] .modal-footer {
  border-color: var(--border-dark);
}

/* 暗色模式下的骨架屏 */
[data-theme="dark"] .skeleton {
  background-image: linear-gradient(
    90deg,
    var(--background-dark) 0%,
    var(--background-light) 50%,
    var(--background-dark) 100%
  );
}

/* 暗色模式文字选择颜色 */
[data-theme="dark"] ::selection {
  background-color: var(--primary-color);
  color: var(--high-contrast-text);
}

/* 暗色模式下的滚动条 */
[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background-color: var(--border-dark);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background-color: var(--primary-color-light);
}

/* 暗色模式动画效果 */
@media (prefers-reduced-motion: no-preference) {
  [data-theme="dark"] .btn:hover,
  [data-theme="dark"] .card:hover {
    transition: all 0.3s ease;
  }
  
  [data-theme="dark"] .ripple {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

/* 暗色模式下的知识图谱节点 */
[data-theme="dark"] .graph-node {
  border-color: var(--border-dark);
  background-color: var(--background-light);
}

[data-theme="dark"] .graph-node.active {
  box-shadow: 0 0 0 2px var(--primary-color), 0 0 15px rgba(var(--primary-color-rgb), 0.5);
}

/* 暗色模式下的图谱连线 */
[data-theme="dark"] .graph-edge {
  stroke: var(--border-color);
}

[data-theme="dark"] .graph-edge.active {
  stroke: var(--primary-color-light);
  filter: drop-shadow(0 0 3px rgba(var(--primary-color-rgb), 0.5));
}

/* 暗色模式下的图谱导航控件 */
[data-theme="dark"] .graph-controls {
  background-color: var(--background-dark);
  border-color: var(--border-dark);
} 