import Cookies from 'js-cookie'

const TokenKey = 'User-Token'
const UserKey = 'User-information'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getUser() {
  const userStr = Cookies.get(UserKey)
  if (userStr) {
    try {
      return JSON.parse(userStr)
    } catch (e) {
      console.error('解析用户信息失败:', e)
      return null
    }
  }
  return null
}

export function setUser(user) {
  return Cookies.set(User<PERSON>ey, JSON.stringify(user))
}

export function removeUser() {
  return Cookies.remove(UserKey)
} 