<template>
  <div>
    <div ref="myPage" style="height:calc(100vh - 60px);" @click="isShowNodeMenuPanel = false">
      <RelationGraph ref="graphRef" :options="graphOptions" :on-node-click="onNodeClick" :on-line-click="onLineClick">
        <template #node="{node}">
          <div>
            <div

              class="c-my-rg-node"
              @click="showNodeMenus(node, $event)"
              @contextmenu.prevent.stop="showNodeMenus(node, $event)"
              @mouseover="nodeSlotOver(node, $event)"
              @mouseout="nodeSlotOut(node, $event)"
            >
                <CircumIcon size="20px" name="cloud_on"/>
            </div>
            <div style="color: forestgreen;font-size: 16px;position: absolute;width: 160px;height:25px;line-height: 25px;margin-top:5px;margin-left:-48px;text-align: center;background-color: rgba(66,187,66,0.2);">
              {{ node.data.myicon }}
            </div>
          </div>
        </template>
      </RelationGraph>
    </div>
    <div v-show="isShowNodeMenuPanel" :style="{left: nodeMenuPanelPosition.x + 'px', top: nodeMenuPanelPosition.y + 'px' }" style="z-index: 999;padding:10px;background-color: #ffffff;border:#eeeeee solid 1px;box-shadow: 0px 0px 8px #cccccc;position: absolute;border-radius: 10px;">
      <div style="line-height: 25px;padding-left: 10px;color: #888888;font-size: 12px;">Perform actions on this node:</div>
      <div class="c-node-menu-item" @click.stop="doAction('Action 1')">Action 1</div>
      <div class="c-node-menu-item" @click.stop="doAction('Action 2')">Action 2</div>
      <div class="c-node-menu-item" @click.stop="doAction('Action 3')">Action 3</div>
      <div class="c-node-menu-item" @click.stop="doAction('Action 4')">Action 4</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import RelationGraph, { RGJsonData, RGNode, RGLine, RGLink, RGUserEvent, RelationGraphComponent } from 'relation-graph-vue3';
import {ElNotification} from "element-plus";
import CircumIcon from "@klarr-agency/circum-icons-vue"; // Vue

const graphRef = ref<RelationGraphComponent>();
const isShowNodeMenuPanel = ref(false);
const currentNode = ref();
const myPage = ref();
const nodeMenuPanelPosition = ref({ x: 0, y: 0 });
const graphOptions = {
    allowSwitchLineShape: true,
    allowSwitchJunctionPoint: true,
    defaultNodeColor: 'rgba(66,187,66,1)',
    defaultJunctionPoint: 'border'
};

const showGraph = async() => {
    const __graph_json_data: RGJsonData = {
        rootId: '2',
        nodes: [
            { id: '1', text: 'Node-1', data: { myicon: 'el-icon-star-on' }},
            { id: '2', text: 'Node-2', data: { myicon: 'el-icon-setting' }},
            { id: '3', text: 'Node-3', data: { myicon: 'el-icon-setting' }},
            { id: '4', text: 'Node-4', data: { myicon: 'el-icon-star-on' }},
            { id: '6', text: 'Node-6', data: { myicon: 'el-icon-setting' }},
            { id: '7', text: 'Node-7', data: { myicon: 'el-icon-setting' }},
            { id: '8', text: 'Node-8', data: { myicon: 'el-icon-star-on' }},
            { id: '9', text: 'Node-9', data: { myicon: 'el-icon-headset' }},
            { id: '71', text: 'Node-71', data: { myicon: 'el-icon-headset' }},
            { id: '72', text: 'Node-72', data: { myicon: 'el-icon-s-tools' }},
            { id: '73', text: 'Node-73', data: { myicon: 'el-icon-star-on' }},
            { id: '81', text: 'Node-81', data: { myicon: 'el-icon-s-promotion' }},
            { id: '82', text: 'Node-82', data: { myicon: 'el-icon-s-promotion' }},
            { id: '83', text: 'Node-83', data: { myicon: 'el-icon-star-on' }},
            { id: '84', text: 'Node-84', data: { myicon: 'el-icon-s-promotion' }},
            { id: '85', text: 'Node-85', data: { myicon: 'el-icon-sunny' }},
            { id: '91', text: 'Node-91', data: { myicon: 'el-icon-sunny' }},
            { id: '92', text: 'Node-82', data: { myicon: 'el-icon-sunny' }},
            { id: '51', text: 'Node-51', data: { myicon: 'el-icon-sunny' }},
            { id: '52', text: 'Node-52', data: { myicon: 'el-icon-sunny' }},
            { id: '53', text: 'Node-53', data: { myicon: 'el-icon-sunny' }},
            { id: '54', text: 'Node-54', data: { myicon: 'el-icon-sunny' }},
            { id: '55', text: 'Node-55', data: { myicon: 'el-icon-sunny' }},
            { id: '5', text: 'Node-5', data: { myicon: 'el-icon-sunny' }}
        ],
        lines: [
            { from: '7', to: '71', text: 'Investment' },
            { from: '7', to: '72', text: 'Investment' },
            { from: '7', to: '73', text: 'Investment' },
            { from: '8', to: '81', text: 'Investment' },
            { from: '8', to: '82', text: 'Investment' },
            { from: '8', to: '83', text: 'Investment' },
            { from: '8', to: '84', text: 'Investment' },
            { from: '8', to: '85', text: 'Investment' },
            { from: '9', to: '91', text: 'Investment' },
            { from: '9', to: '92', text: 'Investment' },
            { from: '5', to: '51', text: 'Investment 1' },
            { from: '5', to: '52', text: 'Investment' },
            { from: '5', to: '53', text: 'Investment 3' },
            { from: '5', to: '54', text: 'Investment 4' },
            { from: '5', to: '55', text: 'Investment' },
            { from: '1', to: '2', text: 'Investment' },
            { from: '3', to: '1', text: 'Executive' },
            { from: '4', to: '2', text: 'Executive' },
            { from: '6', to: '2', text: 'Executive' },
            { from: '7', to: '2', text: 'Executive' },
            { from: '8', to: '2', text: 'Executive' },
            { from: '9', to: '2', text: 'Executive' },
            { from: '1', to: '5', text: 'Investment' }
        ]
    };
    const graphInstance = graphRef.value!.getInstance();
    await graphInstance.setJsonData(__graph_json_data);
    await graphInstance.moveToCenter();
    await graphInstance.zoomToFit();
};

const onNodeClick = (nodeObject: RGNode, $event: RGUserEvent) => {
    console.log('onNodeClick:', nodeObject);
};

const onLineClick = (lineObject: RGLine, linkObject: RGLink, $event: RGUserEvent) => {
    console.log('onLineClick:', lineObject);
};

const nodeSlotOver = (nodeObject: RGNode) => {
    console.log('nodeSlotOver:', nodeObject);
};

const nodeSlotOut = (nodeObject: RGNode) => {
    console.log('nodeSlotOut:', nodeObject);
};

const showNodeMenus = (nodeObject: RGNode, $event: MouseEvent) => {
    currentNode.value = nodeObject;
    const _base_position = myPage.value.getBoundingClientRect();
    console.log('showNodeMenus:', $event.clientX, $event.clientY, _base_position);
    nodeMenuPanelPosition.value.x = $event.clientX - _base_position.x;
    nodeMenuPanelPosition.value.y = $event.clientY - _base_position.y;
    isShowNodeMenuPanel.value = true;
};

const doAction = (actionName: string) => {
    ElNotification({
        title: 'Tip',
        message: 'Performed action ' + actionName + ' on node: ' + currentNode.value.text,
        type: 'success'
    });
    isShowNodeMenuPanel.value = false;
};

onMounted(() => {
    showGraph();
});
</script>

<style lang="scss">

</style>

<style lang="scss" scoped>
.c-my-rg-node {
  height:80px;line-height: 80px;border-radius: 50%;cursor: pointer;
  display: flex;
  place-items: center;
  justify-content: center;
}
.c-node-menu-item{
  line-height: 30px;padding-left: 10px;cursor: pointer;color: #444444;font-size: 14px;border-top:#efefef solid 1px;
}
.c-node-menu-item:hover{
  background-color: rgba(66,187,66,0.2);
}
</style>
