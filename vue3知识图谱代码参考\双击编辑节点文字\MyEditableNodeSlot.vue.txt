<template>
  <MyEditableNodeType4MyGroup
    v-if="node.type === 'my-container'"
    v-bind="props"
    @onNodeTextChange="onNodeTextChange"
    @onNewNodeDropOnContainerNode="onNewNodeDropOnContainerNode"
  />
  <MyEditableNodeType4NormalNode
    v-else
    v-bind="props"
    @onNodeTextChange="onNodeTextChange"
  />
</template>

<script lang="ts" setup>
import {computed, defineEmits} from 'vue';
import {RelationGraphInstance, RGNode} from 'relation-graph-vue3';
import MyEditableNodeType4MyGroup from "./MyEditableNodeType4MyGroup.vue";
import MyEditableNodeType4NormalNode from "./MyEditableNodeType4NormalNode.vue";
const props = defineProps<{
  graphInstance: RelationGraphInstance,
  enableEditingMode: boolean,
  node: RGNode
}>();
const node = computed(() => {
    return props.node;
});
const emit = defineEmits(['onNodeTextChange', 'onNewNodeDropOnContainerNode']);
const onNodeTextChange = (newNodeText: string) => {
    emit('onNodeTextChange', props.node, newNodeText);
};
const onNewNodeDropOnContainerNode = (containerNode: RGNode) => {
    emit('onNewNodeDropOnContainerNode', containerNode);
};
</script>

<style lang="scss" scoped>
</style>
