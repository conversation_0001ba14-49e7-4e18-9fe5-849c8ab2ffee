<template>
  <div>
    <div style="height:calc(100vh);">
      <RelationGraph
        ref="$graphRef"
        :options="graphOptions"
        @canvas-click="onCanvasClick"
        @node-click="onNodeClick"
        @line-click="onLineClick">
        <template #graph-plug>
          <RGMiniToolBar position-h="left" position-v="top" direction="h" />
          <RGEditingController>
            <RGEditingResize />
          </RGEditingController>
        </template>
      </RelationGraph>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import RelationGraph, {RGEditingResize,RGEditingController,RGMiniToolBar} from 'relation-graph-vue3';
import {
    RelationGraphComponent,
    RGJsonData,
    RGLine,
    RGLink,
    RGNode,
    RGSelectionView,
    RGUserEvent
} from 'relation-graph-vue3';
const $graphRef = ref<RelationGraphComponent>(null);
const graphOptions = {
    allowShowMiniToolBar: false
};

onMounted(() => {
    showGraph();
});

const showGraph = () => {
    const __graph_json_data: RGJsonData = {
        rootId: 'a',
        nodes: [
            { id: 'a', text: 'Border color', borderColor: 'yellow' },
            { id: 'a1', text: 'No border', borderWidth: -1, color: '#ff8c00' },
            { id: 'a2', text: 'Plain', borderWidth: 3, color: 'transparent', borderColor: '#ff8c00', fontColor: '#ff8c00' },
            { id: 'a1-1', text: 'Text Node' },
            { id: 'a1-4', text: 'XXX', nodeShape: 0 },
            { id: 'b', text: 'Font color', color: '#43a2f1', fontColor: '#ffd700' },
            { id: 'd', text: 'Node Size', width: 150, height: 150, color: '#ff8c00', borderWidth: 5, borderColor: '#ffd700', fontColor: '#ffffff' },
            { id: 'e', text: 'Rectangular node', nodeShape: 1 },
            { id: 'f', text: 'Rectangular', borderWidth: 1, nodeShape: 1, width: 300, height: 60 },
            { id: 'f1', text: 'Fixed', fixed: true, x: 60, y: 60 },
            { id: 'g', text: 'Css Flash', styleClass: 'my-node-flash-style' }
        ],
        lines: [
            { from: 'a', to: 'b' },
            { from: 'a', to: 'a1' },
            { from: 'a1', to: 'a1-1' },
            { from: 'a', to: 'a2' },
            { from: 'a1', to: 'a1-4' },
            { from: 'a', to: 'f1' },
            { from: 'a', to: 'd' },
            { from: 'd', to: 'f' },
            { from: 'a', to: 'g' },
            { from: 'a', to: 'e' },
            { from: 'b', to: 'e' }
        ]
    };
    $graphRef.value.setJsonData(__graph_json_data, (graphInstance) => {
    });
};

const onNodeClick = (nodeObject: RGNode, $event: RGUserEvent) => {
    console.log('onNodeClick:', nodeObject);
    const graphInstance = $graphRef.value.getInstance();
    graphInstance.setEditingNodes([nodeObject]);
};

const onCanvasClick = () => {
    const graphInstance = $graphRef.value.getInstance();
    graphInstance.setEditingNodes([]);
};

</script>

<style scoped lang="scss">
::v-deep(.relation-graph) {
 .rel-map {
    background-size: 30px 30px;
    background-image: linear-gradient(90deg,rgba(0,0,0,.1) 1px,transparent 0),linear-gradient(180deg,rgba(0,0,0,.1) 1px,transparent 0);
  }
}
</style>
