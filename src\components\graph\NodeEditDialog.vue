<template>
  <div v-if="visible" class="dialog-overlay">
    <div class="dialog-container">
      <div class="dialog-header">
        <h3 class="dialog-title">{{ isNewNode ? '添加节点' : '编辑节点' }}</h3>
        <button class="close-btn" @click="close">×</button>
      </div>
      
      <div class="dialog-body">
        <div class="form-group">
          <label for="node-text">节点名称</label>
          <input 
            type="text" 
            id="node-text" 
            v-model="formData.text" 
            class="form-control"
            placeholder="请输入节点名称"
          />
        </div>
        
        <div class="form-group">
          <label for="node-summary">节点简介</label>
          <textarea 
            id="node-summary" 
            v-model="formData.summary" 
            class="form-control"
            placeholder="请输入节点简介"
            rows="2"
          ></textarea>
        </div>
        
        <div class="form-group">
          <label for="node-content">知识内容</label>
          <div 
            id="node-content" 
            class="rich-text-editor"
            contenteditable="true"
            @input="updateContent"
            v-html="formData.content"
          ></div>
          <div class="editor-toolbar">
            <button @click="applyFormat('bold')" title="加粗"><b>B</b></button>
            <button @click="applyFormat('italic')" title="斜体"><i>I</i></button>
            <button @click="applyFormat('underline')" title="下划线"><u>U</u></button>
            <button @click="applyFormat('insertOrderedList')" title="有序列表">1.</button>
            <button @click="applyFormat('insertUnorderedList')" title="无序列表">•</button>
            <button @click="applyFormat('createLink')" title="插入链接">🔗</button>
          </div>
        </div>
        
        <div class="form-group">
          <label>节点样式</label>
          <div class="style-options">
            <div class="style-option">
              <label for="node-color">颜色</label>
              <input 
                type="color" 
                id="node-color" 
                v-model="formData.color"
                class="color-picker"
              />
            </div>
            
            <div class="style-option">
              <label for="node-font-size">字体大小</label>
              <select id="node-font-size" v-model="formData.fontSize" class="form-control">
                <option value="small">小</option>
                <option value="medium">中</option>
                <option value="large">大</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="node-learning-status">学习状态</label>
          <select id="node-learning-status" v-model="formData.learningStatus" class="form-control">
            <option value="not-started">未学习</option>
            <option value="in-progress">学习中</option>
            <option value="completed">已掌握</option>
          </select>
        </div>
      </div>
      
      <div class="dialog-footer">
        <button class="btn btn-outline" @click="close">取消</button>
        <button class="btn btn-primary" @click="save">保存</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  visible: Boolean,
  node: Object,
  isNewNode: {
    type: Boolean,
    default: false
  },
  parentNode: Object
});

const emit = defineEmits(['close', 'save']);

const formData = ref({
  id: '',
  text: '',
  summary: '',
  content: '',
  color: '#3498db',
  fontSize: 'medium',
  learningStatus: 'not-started'
});

// 监听node属性变化，更新表单数据
watch(() => props.node, (newNode) => {
  if (newNode) {
    formData.value = {
      id: newNode.id || '',
      text: newNode.text || '',
      summary: newNode.data?.summary || '',
      content: newNode.data?.content || '',
      color: newNode.data?.color || '#3498db',
      fontSize: newNode.data?.fontSize || 'medium',
      learningStatus: newNode.data?.learningStatus || 'not-started'
    };
  } else {
    // 如果是新节点，重置表单
    resetForm();
  }
}, { immediate: true });

const resetForm = () => {
  formData.value = {
    id: '',
    text: '',
    summary: '',
    content: '',
    color: '#3498db',
    fontSize: 'medium',
    learningStatus: 'not-started'
  };
};

const close = () => {
  emit('close');
};

const save = () => {
  // 构建节点数据
  const nodeData = {
    id: formData.value.id || generateId(),
    text: formData.value.text,
    data: {
      summary: formData.value.summary,
      content: formData.value.content,
      color: formData.value.color,
      fontSize: formData.value.fontSize,
      learningStatus: formData.value.learningStatus
    }
  };
  
  emit('save', nodeData, props.parentNode);
  close();
};

const generateId = () => {
  return 'node_' + Date.now();
};

const updateContent = (event) => {
  formData.value.content = event.target.innerHTML;
};

const applyFormat = (command, value = null) => {
  document.execCommand(command, false, value);
  
  // 如果是插入链接，弹出输入框
  if (command === 'createLink') {
    const url = prompt('请输入链接地址:', 'http://');
    if (url) {
      document.execCommand('createLink', false, url);
    }
  }
  
  // 更新内容
  const editor = document.getElementById('node-content');
  if (editor) {
    formData.value.content = editor.innerHTML;
  }
};
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-container {
  background-color: var(--background-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.dialog-title {
  margin: 0;
  font-size: var(--font-size-large);
}

.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-large);
  cursor: pointer;
  color: var(--text-light);
}

.dialog-body {
  padding: var(--spacing-md);
  overflow-y: auto;
  flex: 1;
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-normal);
}

.rich-text-editor {
  min-height: 150px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm);
  overflow-y: auto;
  background-color: white;
}

.editor-toolbar {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.editor-toolbar button {
  width: 30px;
  height: 30px;
  border: 1px solid var(--border-color);
  background-color: var(--background-light);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editor-toolbar button:hover {
  background-color: var(--background-color);
}

.style-options {
  display: flex;
  gap: var(--spacing-md);
}

.style-option {
  flex: 1;
}

.color-picker {
  width: 100%;
  height: 40px;
  padding: 0;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}
</style> 