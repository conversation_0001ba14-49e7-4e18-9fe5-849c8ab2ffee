<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教案解析调试</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-section {
            border: 1px solid #ccc;
            margin: 10px 0;
            padding: 15px;
            background: #f9f9f9;
        }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>教案解析调试</h1>
    
    <div class="debug-section">
        <h2>测试内容</h2>
        <pre id="testContent">[测试教案]

### 模块一###
这是模块一的内容。
包含多行文本。

### 模块二###
这是模块二的内容。
也包含多行。

### 模块三###
这是模块三的内容。
最后一个模块。

结束</pre>
    </div>

    <button onclick="debugParse()">开始调试解析</button>
    
    <div id="results"></div>

    <script type="module">
        // 复制解析函数进行调试
        function cleanModuleContent(content) {
            if (!content) return '';
            
            return content
                .trim()
                .replace(/\n{3,}/g, '\n\n')
                .split('\n')
                .map(line => line.trim())
                .join('\n')
                .replace(/^\n+|\n+$/g, '')
                .trim();
        }

        function debugParseLessonPlan(content) {
            console.log('=== 开始调试解析 ===');
            console.log('原始内容长度:', content.length);
            
            // 提取教案标题
            const titleMatch = content.match(/\[([^\]]+)\]/);
            const title = titleMatch ? titleMatch[1].trim() : '未命名教案';
            console.log('教案标题:', title);

            // 提取所有模块标题和位置
            const moduleRegex = /###\s*([^#]+?)\s*###/g;
            const modulePositions = [];
            let match;

            console.log('\n=== 查找模块标题 ===');
            while ((match = moduleRegex.exec(content)) !== null) {
                const position = {
                    title: match[1].trim(),
                    titleStart: match.index,
                    titleEnd: match.index + match[0].length,
                    contentStart: match.index + match[0].length,
                    contentEnd: 0
                };
                
                console.log(`模块: "${position.title}"`);
                console.log(`  标题开始位置: ${position.titleStart}`);
                console.log(`  标题结束位置: ${position.titleEnd}`);
                console.log(`  标题文本: "${content.substring(position.titleStart, position.titleEnd)}"`);
                console.log(`  内容开始位置: ${position.contentStart}`);
                
                modulePositions.push(position);
            }

            // 计算每个模块的内容边界
            console.log('\n=== 计算内容边界 ===');
            for (let i = 0; i < modulePositions.length; i++) {
                const current = modulePositions[i];
                const next = modulePositions[i + 1];
                
                if (next) {
                    current.contentEnd = next.titleStart;
                } else {
                    current.contentEnd = content.length;
                }
                
                console.log(`模块 ${i + 1}: "${current.title}"`);
                console.log(`  内容范围: ${current.contentStart} - ${current.contentEnd}`);
                
                // 提取原始内容
                const rawContent = content.substring(current.contentStart, current.contentEnd);
                console.log(`  原始内容: "${rawContent}"`);
                
                // 清理后的内容
                const cleanedContent = cleanModuleContent(rawContent);
                console.log(`  清理后内容: "${cleanedContent}"`);
                console.log('---');
            }

            // 提取模块内容
            const modules = [];
            for (let i = 0; i < modulePositions.length; i++) {
                const current = modulePositions[i];
                
                let moduleContent = content.substring(current.contentStart, current.contentEnd);
                moduleContent = cleanModuleContent(moduleContent);

                if (moduleContent && moduleContent.trim()) {
                    modules.push({
                        title: current.title,
                        content: moduleContent,
                        sort: i + 1
                    });
                }
            }

            return { title, modules };
        }

        window.debugParse = function() {
            const content = document.getElementById('testContent').textContent;
            const resultsDiv = document.getElementById('results');
            
            try {
                const result = debugParseLessonPlan(content);
                
                let html = '<div class="debug-section"><h2>解析结果</h2>';
                html += `<p class="info">教案标题: ${result.title}</p>`;
                html += `<p class="info">模块数量: ${result.modules.length}</p>`;
                
                result.modules.forEach((module, index) => {
                    html += `<div class="debug-section">`;
                    html += `<h3>模块 ${index + 1}: ${module.title}</h3>`;
                    html += `<p><strong>内容:</strong></p>`;
                    html += `<pre>"${module.content}"</pre>`;
                    html += `<p><strong>内容长度:</strong> ${module.content.length} 字符</p>`;
                    
                    // 检查是否包含标题
                    if (module.content.includes(`### ${module.title} ###`)) {
                        html += `<p class="error">❌ 错误：内容包含了模块标题</p>`;
                    } else {
                        html += `<p class="success">✅ 正确：内容不包含模块标题</p>`;
                    }
                    
                    html += `</div>`;
                });
                
                html += '</div>';
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="debug-section error"><h2>解析失败</h2><p>${error.message}</p></div>`;
            }
        };
    </script>
</body>
</html>
