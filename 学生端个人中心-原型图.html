<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生端个人中心 - AI赋能OBE智慧教学平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8fafc;
            line-height: 1.6;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logo {
            width: 70px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            letter-spacing: 1px;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .title-section h1 {
            font-size: 20px;
            color: #1e293b;
            margin-bottom: 2px;
        }
        
        .title-section p {
            font-size: 12px;
            color: #64748b;
        }
        

        
        .container {
            display: flex;
            height: calc(100vh - 70px);
        }
        
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 30px 0;
        }
        
        .sidebar-header {
            padding: 0 30px 20px;
            border-bottom: 1px solid #f1f5f9;
            margin-bottom: 20px;
        }
        
        .sidebar-header h2 {
            font-size: 18px;
            color: #1e293b;
            font-weight: 600;
        }
        
        .menu-list {
            padding: 0 20px;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: 8px;
            text-decoration: none;
            color: #64748b;
            transition: all 0.3s;
            font-size: 14px;
        }
        
        .menu-item:hover {
            background: #f8fafc;
            color: #1e293b;
        }
        
        .menu-item.active {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        .menu-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .menu-icon.course { background: linear-gradient(135deg, #10b981, #059669); color: white; }
        .menu-icon.knowledge { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; }
        .menu-icon.homework { background: linear-gradient(135deg, #f59e0b, #d97706); color: white; }
        .menu-icon.discussion { background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; }
        .menu-icon.group { background: linear-gradient(135deg, #667eea, #764ba2); color: white; }
        .menu-icon.ai { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; }
        .menu-icon.resources { background: linear-gradient(135deg, #10b981, #047857); color: white; }
        .menu-icon.settings { background: linear-gradient(135deg, #64748b, #475569); color: white; }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .content-header {
            background: white;
            padding: 30px;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .content-header h3 {
            font-size: 24px;
            color: #1e293b;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .content-header p {
            font-size: 14px;
            color: #64748b;
        }
        
        .content-body {
            padding: 30px;
            flex: 1;
            overflow-y: auto;
        }
        
        .page-content {
            width: 100%;
            display: none;
        }

        .page-content.active {
            display: block;
        }
        
        .filter-tabs {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .filter-tab {
            padding: 10px 20px;
            border: none;
            background: #f8fafc;
            color: #64748b;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            font-weight: 500;
        }
        
        .filter-tab.active {
            background: #10b981;
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }
        
        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
        }
        
        .course-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            border: 1px solid #f1f5f9;
        }
        
        .course-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .course-header h4 {
            font-size: 18px;
            color: #1e293b;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .course-header p {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 20px;
        }
        
        .course-body {
            margin-bottom: 20px;
        }
        
        .course-teacher {
            font-size: 13px;
            color: #64748b;
            margin-bottom: 8px;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s;
        }
        
        .progress-fill.green { background: linear-gradient(90deg, #10b981, #059669); }
        .progress-fill.blue { background: linear-gradient(90deg, #3b82f6, #2563eb); }
        .progress-fill.orange { background: linear-gradient(90deg, #f59e0b, #d97706); }
        
        .course-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .btn-secondary {
            background: #f8fafc;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #f1f5f9;
            color: #1e293b;
        }
        
        .btn-primary.green { background: linear-gradient(135deg, #10b981, #059669); }
        .btn-primary.blue { background: linear-gradient(135deg, #3b82f6, #2563eb); }
        .btn-primary.orange { background: linear-gradient(135deg, #f59e0b, #d97706); }
        .btn-primary.purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
        
        .btn-secondary.green { background: rgba(16, 185, 129, 0.1); color: #059669; border-color: rgba(16, 185, 129, 0.2); }
        .btn-secondary.blue { background: rgba(59, 130, 246, 0.1); color: #2563eb; border-color: rgba(59, 130, 246, 0.2); }
        .btn-secondary.orange { background: rgba(245, 158, 11, 0.1); color: #d97706; border-color: rgba(245, 158, 11, 0.2); }
        .btn-secondary.purple { background: rgba(139, 92, 246, 0.1); color: #7c3aed; border-color: rgba(139, 92, 246, 0.2); }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo-section">
            <div class="logo">HiProf</div>
            <div class="title-section">
                <h1>AI赋能OBE智慧教学平台</h1>
                <p>学生学习中心</p>
            </div>
        </div>

    </header>

    <div class="container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>个人中心</h2>
            </div>
            <nav class="menu-list">
                <a href="#" class="menu-item active" data-content="course">
                    <div class="menu-icon course">课</div>
                    <span>课程</span>
                </a>
                <a href="#" class="menu-item" data-content="knowledge">
                    <div class="menu-icon knowledge">图</div>
                    <span>知识图谱</span>
                </a>
                <a href="#" class="menu-item" data-content="homework">
                    <div class="menu-icon homework">作</div>
                    <span>作业</span>
                </a>
                <a href="#" class="menu-item" data-content="discussion">
                    <div class="menu-icon discussion">论</div>
                    <span>讨论</span>
                </a>
                <a href="#" class="menu-item" data-content="group">
                    <div class="menu-icon group">组</div>
                    <span>小组</span>
                </a>
                <a href="#" class="menu-item" data-content="ai">
                    <div class="menu-icon ai">AI</div>
                    <span>AI助手</span>
                </a>
                <a href="#" class="menu-item" data-content="resources">
                    <div class="menu-icon resources">资</div>
                    <span>教师共享资料</span>
                </a>
                <a href="#" class="menu-item" data-content="settings">
                    <div class="menu-icon settings">设</div>
                    <span>设置</span>
                </a>
            </nav>
        </aside>

        <main class="content-area">
            <div class="content-header">
                <h3 id="content-title">我的课程</h3>
                <p id="content-description">查看和管理您的学习课程</p>
            </div>
            <div class="content-body">
                <!-- 课程页面 -->
                <div id="course-content" class="page-content active">
                    <div class="filter-tabs">
                        <button class="filter-tab">全部课程</button>
                        <button class="filter-tab active">进行中</button>
                        <button class="filter-tab">已完成</button>
                    </div>

                    <div class="courses-grid">
                        <div class="course-card">
                            <div class="course-header">
                                <h4>结构力学</h4>
                                <p>土木工程核心基础课程</p>
                            </div>
                            <div class="course-body">
                                <div class="course-teacher">授课教师：张教授</div>
                                <div class="progress-info">
                                    <span>学习进度</span>
                                    <span>75%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill green" style="width: 75%"></div>
                                </div>
                                <div class="course-actions">
                                    <button class="btn btn-primary green">继续学习</button>
                                    <button class="btn btn-secondary green">查看详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 知识图谱页面 -->
                <div id="knowledge-content" class="page-content">
                    <div class="knowledge-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <div class="filter-tabs">
                            <button class="filter-tab active">全部图谱</button>
                            <button class="filter-tab">已学习</button>
                            <button class="filter-tab">学习中</button>
                            <button class="filter-tab">未开始</button>
                        </div>
                        <div class="search-box">
                            <input type="text" placeholder="搜索知识图谱..." style="padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 4px; width: 200px;">
                        </div>
                    </div>

                    <div class="knowledge-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                        <!-- 知识图谱卡片1 -->
                        <div class="knowledge-card" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
                            <div class="knowledge-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <div>
                                    <h4 style="font-size: 16px; color: #1e293b; margin-bottom: 4px;">结构力学基础</h4>
                                    <p style="font-size: 12px; color: #64748b;">结构力学</p>
                                </div>
                                <span class="progress-status learning" style="padding: 4px 8px; background: #dcfce7; color: #166534; border-radius: 12px; font-size: 10px; font-weight: bold;">学习中</span>
                            </div>

                            <div class="knowledge-progress" style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; font-size: 12px; color: #64748b;">
                                    <span>学习进度</span>
                                    <span>75%</span>
                                </div>
                                <div class="progress-bar" style="width: 100%; height: 8px; background: #e2e8f0; border-radius: 4px; overflow: hidden;">
                                    <div class="progress-fill" style="width: 75%; height: 100%; background: #10b981; border-radius: 4px;"></div>
                                </div>
                            </div>

                            <div class="knowledge-actions" style="display: flex; gap: 10px;">
                                <button class="btn btn-primary green">查看图谱</button>
                                <button class="btn btn-secondary green">继续学习</button>
                            </div>
                        </div>

                        <!-- 知识图谱卡片2 -->
                        <div class="knowledge-card" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
                            <div class="knowledge-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <div>
                                    <h4 style="font-size: 16px; color: #1e293b; margin-bottom: 4px;">混凝土材料性能</h4>
                                    <p style="font-size: 12px; color: #64748b;">混凝土结构设计</p>
                                </div>
                                <span class="progress-status completed" style="padding: 4px 8px; background: #dbeafe; color: #1e40af; border-radius: 12px; font-size: 10px; font-weight: bold;">已完成</span>
                            </div>

                            <div class="knowledge-progress" style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; font-size: 12px; color: #64748b;">
                                    <span>学习进度</span>
                                    <span>100%</span>
                                </div>
                                <div class="progress-bar" style="width: 100%; height: 8px; background: #e2e8f0; border-radius: 4px; overflow: hidden;">
                                    <div class="progress-fill" style="width: 100%; height: 100%; background: #3b82f6; border-radius: 4px;"></div>
                                </div>
                            </div>

                            <div class="knowledge-actions" style="display: flex; gap: 10px;">
                                <button class="btn btn-primary blue">查看图谱</button>
                                <button class="btn btn-secondary blue">复习巩固</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 作业页面 -->
                <div id="homework-content" class="page-content">
                    <div class="homework-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <div class="homework-tabs" style="display: flex; gap: 20px;">
                            <button class="filter-tab active">待完成</button>
                            <button class="filter-tab">已提交</button>
                            <button class="filter-tab">已批改</button>
                        </div>
                        <div class="homework-stats" style="display: flex; gap: 20px; font-size: 12px; color: #64748b;">
                            <span>📝 待完成: 3个</span>
                            <span>✅ 已提交: 8个</span>
                            <span>📊 已批改: 5个</span>
                        </div>
                    </div>

                    <div class="homework-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 24px;">
                        <!-- 作业卡片1 -->
                        <div class="homework-card" style="background: white; border-radius: 16px; padding: 24px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9; transition: all 0.3s;">
                            <div class="homework-header" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
                                <div style="flex: 1;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                        <div style="width: 6px; height: 6px; background: #10b981; border-radius: 50%;"></div>
                                        <h4 style="font-size: 18px; color: #1e293b; font-weight: 600; margin: 0;">结构力学计算作业</h4>
                                    </div>
                                    <p style="font-size: 13px; color: #64748b; margin: 0;">第五章 - 静定结构内力分析</p>
                                    <p style="font-size: 12px; color: #94a3b8; margin: 4px 0 0 0;">结构力学 · 张教授</p>
                                </div>
                                <span class="status-badge urgent" style="padding: 6px 12px; background: linear-gradient(135deg, #fef2f2, #fee2e2); color: #dc2626; border-radius: 20px; font-size: 11px; font-weight: 600; border: 1px solid #fecaca;">紧急</span>
                            </div>

                            <div class="homework-meta" style="background: rgba(16, 185, 129, 0.05); border: 1px solid rgba(16, 185, 129, 0.1); border-radius: 12px; padding: 16px; margin-bottom: 20px;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                                    <div style="display: flex; align-items: center; gap: 6px;">
                                        <span style="font-size: 14px;">📅</span>
                                        <span style="font-size: 12px; color: #64748b;">发布: 12-10</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 6px;">
                                        <span style="font-size: 14px;">⏰</span>
                                        <span style="font-size: 12px; color: #dc2626; font-weight: 500;">截止: 12-17 23:59</span>
                                    </div>
                                </div>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                                    <div style="display: flex; align-items: center; gap: 6px;">
                                        <span style="font-size: 14px;">📝</span>
                                        <span style="font-size: 12px; color: #64748b;">3题</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 6px;">
                                        <span style="font-size: 14px;">💯</span>
                                        <span style="font-size: 12px; color: #64748b;">100分</span>
                                    </div>
                                </div>
                            </div>

                            <div class="homework-description" style="background: rgba(248, 250, 252, 0.8); border-left: 3px solid #10b981; padding: 12px 16px; border-radius: 0 8px 8px 0; margin-bottom: 20px;">
                                <p style="font-size: 13px; color: #475569; line-height: 1.5; margin: 0;">
                                    完成简支梁、连续梁的内力计算，绘制弯矩图和剪力图，要求提交手算过程和CAD图纸
                                </p>
                            </div>

                            <div class="homework-actions" style="display: flex; gap: 8px;">
                                <button class="btn btn-primary green" style="flex: 1; padding: 12px 20px; font-size: 14px; font-weight: 600; border-radius: 10px;">开始作业</button>
                                <button class="btn btn-secondary green" style="padding: 10px 16px; font-size: 13px; border-radius: 8px;">详情</button>
                                <button class="btn btn-secondary green" style="padding: 10px 16px; font-size: 13px; border-radius: 8px;">附件</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 讨论页面 -->
                <div id="discussion-content" class="page-content">
                    <!-- 页面头部 -->
                    <div class="discussion-page-header" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); border-radius: 16px; padding: 30px; margin-bottom: 30px; color: white; position: relative; overflow: hidden;">
                        <!-- 装饰性背景元素 -->
                        <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.6;"></div>
                        <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.08); border-radius: 50%;"></div>

                        <div style="position: relative; z-index: 1;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <div>
                                    <h2 style="font-size: 28px; font-weight: 700; margin-bottom: 8px; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">课程讨论</h2>
                                    <p style="font-size: 16px; opacity: 0.9; margin: 0;">分享知识，交流学习心得</p>
                                </div>
                                <button class="btn btn-primary" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 12px 24px; font-size: 14px; font-weight: 600; border-radius: 12px; backdrop-filter: blur(10px); transition: all 0.3s; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">+ 发起讨论</button>
                            </div>

                            <!-- 统计信息 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 20px; margin-top: 20px;">
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">156</div>
                                    <div style="font-size: 12px; opacity: 0.8;">总讨论数</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">12</div>
                                    <div style="font-size: 12px; opacity: 0.8;">我的发帖</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">28</div>
                                    <div style="font-size: 12px; opacity: 0.8;">我的回复</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">5</div>
                                    <div style="font-size: 12px; opacity: 0.8;">今日新增</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 导航标签 -->
                    <div class="discussion-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <div class="discussion-tabs" style="display: flex; gap: 8px; background: #f8fafc; padding: 6px; border-radius: 12px; border: 1px solid #e2e8f0;">
                            <button class="filter-tab active" style="padding: 10px 20px; border-radius: 8px; border: none; background: white; color: #8b5cf6; font-weight: 600; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: all 0.3s;">全部讨论</button>
                            <button class="filter-tab" style="padding: 10px 20px; border-radius: 8px; border: none; background: transparent; color: #64748b; font-weight: 500; transition: all 0.3s;">我的发帖</button>
                            <button class="filter-tab" style="padding: 10px 20px; border-radius: 8px; border: none; background: transparent; color: #64748b; font-weight: 500; transition: all 0.3s;">我的回复</button>
                            <button class="filter-tab" style="padding: 10px 20px; border-radius: 8px; border: none; background: transparent; color: #64748b; font-weight: 500; transition: all 0.3s;">热门话题</button>
                        </div>
                        <div style="display: flex; gap: 12px; align-items: center;">
                            <select style="padding: 10px 16px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px;">
                                <option>最新回复</option>
                                <option>最多回复</option>
                                <option>最多浏览</option>
                                <option>发布时间</option>
                            </select>
                            <input type="text" placeholder="搜索讨论话题..." style="padding: 10px 16px; border: 1px solid #e2e8f0; border-radius: 8px; width: 200px; font-size: 14px;">
                            <button style="padding: 10px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; cursor: pointer;">🔍</button>
                        </div>
                    </div>

                    <!-- 讨论列表 -->
                    <div class="discussion-list" style="display: flex; flex-direction: column; gap: 20px;">
                        <!-- 讨论话题1 -->
                        <div class="discussion-item" style="background: white; border-radius: 16px; padding: 24px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9; transition: all 0.3s;">
                            <div class="discussion-badge" style="display: inline-block; padding: 4px 12px; background: linear-gradient(135deg, #fef2f2, #fee2e2); color: #dc2626; border-radius: 20px; font-size: 11px; font-weight: 700; margin-bottom: 16px;">🔥 热门</div>
                            <div class="discussion-header" style="display: flex; gap: 16px; margin-bottom: 16px;">
                                <div class="user-avatar" style="width: 48px; height: 48px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 18px; flex-shrink: 0; cursor: pointer;">王</div>
                                <div style="flex: 1;">
                                    <h4 style="font-size: 18px; color: #1e293b; margin: 0 0 8px 0; font-weight: 700; cursor: pointer;">关于结构力学学习方法的讨论</h4>
                                    <div style="display: flex; align-items: center; gap: 12px; font-size: 13px; color: #64748b; margin-bottom: 8px;">
                                        <span>王同学</span>
                                        <span>•</span>
                                        <span>结构力学课程</span>
                                        <span>•</span>
                                        <span>2小时前</span>
                                    </div>
                                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                        <span style="padding: 4px 8px; background: rgba(245, 158, 11, 0.1); color: #d97706; border-radius: 12px; font-size: 11px; font-weight: 600; cursor: pointer;">#结构力学</span>
                                        <span style="padding: 4px 8px; background: rgba(245, 158, 11, 0.1); color: #d97706; border-radius: 12px; font-size: 11px; font-weight: 600; cursor: pointer;">#内力分析</span>
                                        <span style="padding: 4px 8px; background: rgba(245, 158, 11, 0.1); color: #d97706; border-radius: 12px; font-size: 11px; font-weight: 600; cursor: pointer;">#学习方法</span>
                                    </div>
                                </div>
                            </div>

                            <div class="discussion-content" style="margin-bottom: 20px;">
                                <p style="font-size: 14px; color: #475569; line-height: 1.6; margin-bottom: 16px;">
                                    大家好，我在学习结构力学时遇到了一些困难，特别是在理解静定结构内力分析方面。想请教一下大家有什么好的学习方法和技巧，比如如何更好地理解力的传递路径...
                                </p>
                                <div class="latest-reply" style="background: rgba(248, 250, 252, 0.8); border-left: 3px solid #3b82f6; padding: 12px 16px; border-radius: 0 8px 8px 0;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                        <div class="user-avatar" style="width: 24px; height: 24px; background: linear-gradient(135deg, #10b981, #059669); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px; cursor: pointer;">李</div>
                                        <span style="font-size: 12px; color: #64748b;">李同学 最新回复:</span>
                                    </div>
                                    <p style="font-size: 13px; color: #475569; line-height: 1.5; margin: 0;">
                                        "我推荐先从简单的静定梁开始，比如简支梁和悬臂梁，然后逐步理解力的平衡条件和内力图绘制..."
                                    </p>
                                </div>
                            </div>

                            <div class="discussion-stats" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                <div style="display: flex; gap: 20px; font-size: 12px; color: #64748b;">
                                    <div style="display: flex; align-items: center; gap: 4px;">
                                        <span style="font-size: 16px;">👥</span>
                                        <span>15</span>
                                        <span>参与人数</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 4px;">
                                        <span style="font-size: 16px;">💬</span>
                                        <span>28</span>
                                        <span>回复数</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 4px;">
                                        <span style="font-size: 16px;">👁️</span>
                                        <span>156</span>
                                        <span>浏览量</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 4px;">
                                        <span style="font-size: 16px;">⏰</span>
                                        <span>2h</span>
                                        <span>最后回复</span>
                                    </div>
                                </div>
                            </div>

                            <div class="discussion-actions" style="display: flex; gap: 8px;">
                                <button class="btn btn-primary orange" style="flex: 1; padding: 12px 20px; font-size: 14px; font-weight: 600; border-radius: 10px;">查看详情</button>
                                <button class="btn btn-secondary orange" style="padding: 12px 20px; font-size: 14px; border-radius: 10px;">参与讨论</button>
                                <button style="padding: 12px; border: 1px solid rgba(245, 158, 11, 0.2); border-radius: 10px; background: rgba(245, 158, 11, 0.1); color: #d97706; cursor: pointer; transition: all 0.3s;">⭐</button>
                                <button style="padding: 12px; border: 1px solid rgba(245, 158, 11, 0.2); border-radius: 10px; background: rgba(245, 158, 11, 0.1); color: #d97706; cursor: pointer; transition: all 0.3s;">📤</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 小组页面 -->
                <div id="group-content" class="page-content">
                    <!-- 页面头部 -->
                    <div class="group-page-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 16px; padding: 30px; margin-bottom: 30px; color: white; position: relative; overflow: hidden;">
                        <!-- 装饰性背景元素 -->
                        <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.6;"></div>
                        <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.08); border-radius: 50%;"></div>

                        <div style="position: relative; z-index: 1;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <div>
                                    <h2 style="font-size: 28px; font-weight: 700; margin-bottom: 8px; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">小组协作</h2>
                                    <p style="font-size: 16px; opacity: 0.9; margin: 0;">与同学一起学习，共同进步</p>
                                </div>
                                <button class="btn btn-primary" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 12px 24px; font-size: 14px; font-weight: 600; border-radius: 12px; backdrop-filter: blur(10px); transition: all 0.3s; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">+ 创建小组</button>
                            </div>

                            <!-- 统计信息 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 20px; margin-top: 20px;">
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">2</div>
                                    <div style="font-size: 12px; opacity: 0.8;">我的小组</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">3</div>
                                    <div style="font-size: 12px; opacity: 0.8;">进行中项目</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">9</div>
                                    <div style="font-size: 12px; opacity: 0.8;">总成员数</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">5</div>
                                    <div style="font-size: 12px; opacity: 0.8;">可加入小组</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 导航标签 -->
                    <div class="group-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <div class="group-tabs" style="display: flex; gap: 8px; background: #f8fafc; padding: 6px; border-radius: 12px; border: 1px solid #e2e8f0;">
                            <button class="filter-tab active" style="padding: 10px 20px; border-radius: 8px; border: none; background: white; color: #667eea; font-weight: 600; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: all 0.3s;">我的小组</button>
                            <button class="filter-tab" style="padding: 10px 20px; border-radius: 8px; border: none; background: transparent; color: #64748b; font-weight: 500; transition: all 0.3s;">可加入</button>
                            <button class="filter-tab" style="padding: 10px 20px; border-radius: 8px; border: none; background: transparent; color: #64748b; font-weight: 500; transition: all 0.3s;">已完成</button>
                        </div>
                        <div style="display: flex; gap: 12px; align-items: center;">
                            <select style="padding: 10px 16px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px;">
                                <option>最新创建</option>
                                <option>最多成员</option>
                                <option>最活跃</option>
                            </select>
                            <input type="text" placeholder="搜索小组..." style="padding: 10px 16px; border: 1px solid #e2e8f0; border-radius: 8px; width: 200px; font-size: 14px;">
                        </div>
                    </div>

                    <!-- 小组列表 -->
                    <div class="groups-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 24px;">
                        <!-- 小组卡片1 -->
                        <div class="group-card" style="background: white; border-radius: 16px; padding: 24px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9; transition: all 0.3s;">
                            <div class="group-header" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
                                <div style="flex: 1;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                        <div style="width: 8px; height: 8px; background: #10b981; border-radius: 50%;"></div>
                                        <h4 style="font-size: 18px; color: #1e293b; font-weight: 600; margin: 0;">结构设计学习小组</h4>
                                    </div>
                                    <p style="font-size: 13px; color: #64748b; margin: 0;">结构力学课程学习小组</p>
                                    <p style="font-size: 12px; color: #94a3b8; margin: 4px 0 0 0;">创建于 2024-11-15</p>
                                </div>
                                <span class="status-badge active" style="padding: 6px 12px; background: linear-gradient(135deg, #dcfce7, #bbf7d0); color: #166534; border-radius: 20px; font-size: 11px; font-weight: 600; border: 1px solid #86efac;">活跃</span>
                            </div>

                            <div class="group-description" style="background: rgba(16, 185, 129, 0.05); border: 1px solid rgba(16, 185, 129, 0.1); border-radius: 12px; padding: 16px; margin-bottom: 20px;">
                                <p style="font-size: 13px; color: #475569; line-height: 1.5; margin: 0;">
                                    专注于结构力学理论学习和实践应用，定期组织讨论会，分享学习资料和解题技巧
                                </p>
                            </div>

                            <div class="group-members" style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                    <span style="font-size: 12px; color: #64748b; font-weight: 500;">小组成员</span>
                                    <span style="font-size: 12px; color: #64748b;">5/8人</span>
                                </div>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <div class="member-avatars" style="display: flex; gap: -8px;">
                                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px; border: 2px solid white; margin-left: -8px; cursor: pointer;">王</div>
                                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #10b981, #059669); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px; border: 2px solid white; margin-left: -8px; cursor: pointer;">李</div>
                                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #3b82f6, #2563eb); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px; border: 2px solid white; margin-left: -8px; cursor: pointer;">张</div>
                                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px; border: 2px solid white; margin-left: -8px; cursor: pointer;">刘</div>
                                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #ec4899, #db2777); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px; border: 2px solid white; margin-left: -8px; cursor: pointer;">陈</div>
                                    </div>
                                    <span style="font-size: 11px; color: #94a3b8; margin-left: 8px;">+3个空位</span>
                                </div>
                            </div>

                            <div class="group-stats" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin-bottom: 20px;">
                                <div style="text-align: center; padding: 12px; background: rgba(248, 250, 252, 0.8); border-radius: 8px;">
                                    <div style="font-size: 16px; font-weight: 700; color: #1e293b; margin-bottom: 2px;">12</div>
                                    <div style="font-size: 10px; color: #64748b;">讨论话题</div>
                                </div>
                                <div style="text-align: center; padding: 12px; background: rgba(248, 250, 252, 0.8); border-radius: 8px;">
                                    <div style="font-size: 16px; font-weight: 700; color: #1e293b; margin-bottom: 2px;">3</div>
                                    <div style="font-size: 10px; color: #64748b;">进行项目</div>
                                </div>
                                <div style="text-align: center; padding: 12px; background: rgba(248, 250, 252, 0.8); border-radius: 8px;">
                                    <div style="font-size: 16px; font-weight: 700; color: #1e293b; margin-bottom: 2px;">85%</div>
                                    <div style="font-size: 10px; color: #64748b;">活跃度</div>
                                </div>
                            </div>

                            <div class="group-actions" style="display: flex; gap: 8px;">
                                <button class="btn btn-primary purple" style="flex: 1; padding: 12px 20px; font-size: 14px; font-weight: 600; border-radius: 10px;">进入小组</button>
                                <button class="btn btn-secondary purple" style="padding: 12px 20px; font-size: 14px; border-radius: 10px;">详情</button>
                                <button style="padding: 12px; border: 1px solid rgba(139, 92, 246, 0.2); border-radius: 10px; background: rgba(139, 92, 246, 0.1); color: #7c3aed; cursor: pointer; transition: all 0.3s;">⚙️</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI助手页面 -->
                <div id="ai-content" class="page-content">
                    <!-- 页面头部 -->
                    <div class="ai-page-header" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); border-radius: 16px; padding: 30px; margin-bottom: 30px; color: white; position: relative; overflow: hidden;">
                        <!-- 装饰性背景元素 -->
                        <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.6;"></div>
                        <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.08); border-radius: 50%;"></div>

                        <div style="position: relative; z-index: 1;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <div>
                                    <h2 style="font-size: 28px; font-weight: 700; margin-bottom: 8px; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">AI智能助手</h2>
                                    <p style="font-size: 16px; opacity: 0.9; margin: 0;">智能学习伙伴，随时为您答疑解惑</p>
                                </div>
                                <button class="btn btn-primary" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 12px 24px; font-size: 14px; font-weight: 600; border-radius: 12px; backdrop-filter: blur(10px); transition: all 0.3s; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">🤖 新对话</button>
                            </div>

                            <!-- 统计信息 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 20px; margin-top: 20px;">
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">42</div>
                                    <div style="font-size: 12px; opacity: 0.8;">总对话数</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">156</div>
                                    <div style="font-size: 12px; opacity: 0.8;">解答问题</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">8</div>
                                    <div style="font-size: 12px; opacity: 0.8;">今日咨询</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">95%</div>
                                    <div style="font-size: 12px; opacity: 0.8;">满意度</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI功能模块 -->
                    <div class="ai-modules" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 30px; margin-bottom: 30px;">
                        <!-- 智能AI回答模块 -->
                        <div class="ai-module-card" style="background: white; border-radius: 16px; padding: 30px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
                            <div class="module-header" style="display: flex; align-items: center; gap: 12px; margin-bottom: 20px;">
                                <div style="width: 48px; height: 48px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 24px;">🤖</div>
                                <div>
                                    <h3 style="font-size: 20px; color: #1e293b; margin: 0 0 4px 0; font-weight: 700;">智能AI回答</h3>
                                    <p style="font-size: 14px; color: #64748b; margin: 0;">智能问答系统，随时为您解答学习疑问</p>
                                </div>
                            </div>

                            <div class="module-content" style="margin-bottom: 24px;">
                                <div class="chat-preview" style="background: rgba(59, 130, 246, 0.05); border: 1px solid rgba(59, 130, 246, 0.1); border-radius: 12px; padding: 20px;">
                                    <div class="chat-message user" style="display: flex; justify-content: flex-end; margin-bottom: 12px;">
                                        <div style="background: #3b82f6; color: white; padding: 10px 16px; border-radius: 18px 18px 4px 18px; max-width: 80%; font-size: 14px;">
                                            请解释一下结构力学中的弯矩概念
                                        </div>
                                    </div>
                                    <div class="chat-message ai" style="display: flex; justify-content: flex-start;">
                                        <div style="background: white; color: #1e293b; padding: 10px 16px; border-radius: 18px 18px 18px 4px; max-width: 80%; font-size: 14px; border: 1px solid #e2e8f0;">
                                            弯矩是结构力学中的重要概念，表示截面上内力矩的大小...
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="module-actions" style="display: flex; gap: 12px;">
                                <button class="btn btn-primary blue" style="flex: 1; padding: 14px 20px; font-size: 16px; font-weight: 600; border-radius: 12px;">开始对话</button>
                                <button class="btn btn-secondary blue" style="padding: 14px 20px; font-size: 14px; border-radius: 12px;">查看历史</button>
                            </div>
                        </div>

                        <!-- 作业辅助模块 -->
                        <div class="ai-module-card" style="background: white; border-radius: 16px; padding: 30px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
                            <div class="module-header" style="display: flex; align-items: center; gap: 12px; margin-bottom: 20px;">
                                <div style="width: 48px; height: 48px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 24px;">✏️</div>
                                <div>
                                    <h3 style="font-size: 20px; color: #1e293b; margin: 0 0 4px 0; font-weight: 700;">作业辅助</h3>
                                    <p style="font-size: 14px; color: #64748b; margin: 0;">智能作业辅导，提供解题思路和步骤指导</p>
                                </div>
                            </div>

                            <div class="module-content" style="margin-bottom: 24px;">
                                <div class="homework-features" style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                    <div class="feature-item" style="background: rgba(245, 158, 11, 0.05); border: 1px solid rgba(245, 158, 11, 0.1); border-radius: 8px; padding: 16px; text-align: center;">
                                        <div style="font-size: 20px; margin-bottom: 8px;">📝</div>
                                        <div style="font-size: 14px; color: #1e293b; font-weight: 600; margin-bottom: 4px;">题目解析</div>
                                        <div style="font-size: 12px; color: #64748b;">详细解题步骤</div>
                                    </div>
                                    <div class="feature-item" style="background: rgba(245, 158, 11, 0.05); border: 1px solid rgba(245, 158, 11, 0.1); border-radius: 8px; padding: 16px; text-align: center;">
                                        <div style="font-size: 20px; margin-bottom: 8px;">💡</div>
                                        <div style="font-size: 14px; color: #1e293b; font-weight: 600; margin-bottom: 4px;">思路指导</div>
                                        <div style="font-size: 12px; color: #64748b;">解题方法提示</div>
                                    </div>
                                    <div class="feature-item" style="background: rgba(245, 158, 11, 0.05); border: 1px solid rgba(245, 158, 11, 0.1); border-radius: 8px; padding: 16px; text-align: center;">
                                        <div style="font-size: 20px; margin-bottom: 8px;">�</div>
                                        <div style="font-size: 14px; color: #1e293b; font-weight: 600; margin-bottom: 4px;">图表绘制</div>
                                        <div style="font-size: 12px; color: #64748b;">力学图表辅助</div>
                                    </div>
                                    <div class="feature-item" style="background: rgba(245, 158, 11, 0.05); border: 1px solid rgba(245, 158, 11, 0.1); border-radius: 8px; padding: 16px; text-align: center;">
                                        <div style="font-size: 20px; margin-bottom: 8px;">🔍</div>
                                        <div style="font-size: 14px; color: #1e293b; font-weight: 600; margin-bottom: 4px;">错误检查</div>
                                        <div style="font-size: 12px; color: #64748b;">作业批改建议</div>
                                    </div>
                                </div>
                            </div>

                            <div class="module-actions" style="display: flex; gap: 12px;">
                                <button class="btn btn-primary orange" style="flex: 1; padding: 14px 20px; font-size: 16px; font-weight: 600; border-radius: 12px;">上传作业</button>
                                <button class="btn btn-secondary orange" style="padding: 14px 20px; font-size: 14px; border-radius: 12px;">使用指南</button>
                            </div>
                        </div>
                    </div>


                </div>

                <!-- 教师共享资料页面 -->
                <div id="resources-content" class="page-content">
                    <!-- 页面头部 -->
                    <div class="resources-page-header" style="background: linear-gradient(135deg, #10b981 0%, #047857 100%); border-radius: 16px; padding: 30px; margin-bottom: 30px; color: white; position: relative; overflow: hidden;">
                        <!-- 装饰性背景元素 -->
                        <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.6;"></div>
                        <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.08); border-radius: 50%;"></div>

                        <div style="position: relative; z-index: 1;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <div>
                                    <h2 style="font-size: 28px; font-weight: 700; margin-bottom: 8px; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">教师共享资料</h2>
                                    <p style="font-size: 16px; opacity: 0.9; margin: 0;">获取优质教学资源，提升学习效果</p>
                                </div>
                                <button class="btn btn-primary" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 12px 24px; font-size: 14px; font-weight: 600; border-radius: 12px; backdrop-filter: blur(10px); transition: all 0.3s; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">📤 上传资料</button>
                            </div>

                            <!-- 统计信息 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 20px; margin-top: 20px;">
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">128</div>
                                    <div style="font-size: 12px; opacity: 0.8;">总资料数</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">45</div>
                                    <div style="font-size: 12px; opacity: 0.8;">已下载</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">12</div>
                                    <div style="font-size: 12px; opacity: 0.8;">本周新增</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">8</div>
                                    <div style="font-size: 12px; opacity: 0.8;">我的收藏</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 导航标签 -->
                    <div class="resources-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                        <div class="resources-tabs" style="display: flex; gap: 8px; background: #f8fafc; padding: 6px; border-radius: 12px; border: 1px solid #e2e8f0;">
                            <button class="filter-tab active" style="padding: 10px 20px; border-radius: 8px; border: none; background: white; color: #10b981; font-weight: 600; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: all 0.3s;">全部资料</button>
                            <button class="filter-tab" style="padding: 10px 20px; border-radius: 8px; border: none; background: transparent; color: #64748b; font-weight: 500; transition: all 0.3s;">课件PPT</button>
                            <button class="filter-tab" style="padding: 10px 20px; border-radius: 8px; border: none; background: transparent; color: #64748b; font-weight: 500; transition: all 0.3s;">习题资料</button>
                            <button class="filter-tab" style="padding: 10px 20px; border-radius: 8px; border: none; background: transparent; color: #64748b; font-weight: 500; transition: all 0.3s;">参考文献</button>
                            <button class="filter-tab" style="padding: 10px 20px; border-radius: 8px; border: none; background: transparent; color: #64748b; font-weight: 500; transition: all 0.3s;">我的收藏</button>
                        </div>
                        <div style="display: flex; gap: 12px; align-items: center;">
                            <select style="padding: 10px 16px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; color: #64748b; font-size: 14px;">
                                <option>最新上传</option>
                                <option>下载最多</option>
                                <option>评分最高</option>
                                <option>文件大小</option>
                            </select>
                            <input type="text" placeholder="搜索资料..." style="padding: 10px 16px; border: 1px solid #e2e8f0; border-radius: 8px; width: 200px; font-size: 14px;">
                        </div>
                    </div>

                    <!-- 资料列表 -->
                    <div class="resources-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 24px;">
                        <!-- 资料卡片1 -->
                        <div class="resource-card" style="background: white; border-radius: 16px; padding: 24px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9; transition: all 0.3s;">
                            <div class="resource-header" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
                                <div style="flex: 1;">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #dc2626, #b91c1c); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 12px;">PPT</div>
                                        <h4 style="font-size: 16px; color: #1e293b; font-weight: 600; margin: 0;">结构力学基础理论</h4>
                                    </div>
                                    <p style="font-size: 12px; color: #64748b; margin: 0;">第一章 - 绪论与基本概念</p>
                                    <p style="font-size: 11px; color: #94a3b8; margin: 4px 0 0 0;">张教授 · 2024-12-10</p>
                                </div>
                                <div style="display: flex; gap: 4px;">
                                    <span style="padding: 4px 8px; background: rgba(16, 185, 129, 0.1); color: #059669; border-radius: 12px; font-size: 10px; font-weight: 600;">新</span>
                                    <span style="padding: 4px 8px; background: rgba(245, 158, 11, 0.1); color: #d97706; border-radius: 12px; font-size: 10px; font-weight: 600;">热门</span>
                                </div>
                            </div>

                            <div class="resource-description" style="background: rgba(16, 185, 129, 0.05); border: 1px solid rgba(16, 185, 129, 0.1); border-radius: 12px; padding: 16px; margin-bottom: 16px;">
                                <p style="font-size: 13px; color: #475569; line-height: 1.5; margin: 0;">
                                    包含结构力学的基本概念、研究对象、基本假设和分析方法，适合初学者系统学习
                                </p>
                            </div>

                            <div class="resource-meta" style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px; font-size: 12px; color: #64748b;">
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <span>📁</span>
                                    <span>2.5 MB</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <span>⬇️</span>
                                    <span>156次下载</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <span>⭐</span>
                                    <span>4.8分</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <span>👁️</span>
                                    <span>328次浏览</span>
                                </div>
                            </div>

                            <div class="resource-actions" style="display: flex; gap: 8px;">
                                <button class="btn btn-primary green" style="flex: 1; padding: 12px 20px; font-size: 14px; font-weight: 600; border-radius: 10px;">下载</button>
                                <button class="btn btn-secondary green" style="padding: 12px 20px; font-size: 14px; border-radius: 10px;">预览</button>
                                <button style="padding: 12px; border: 1px solid rgba(16, 185, 129, 0.2); border-radius: 10px; background: rgba(16, 185, 129, 0.1); color: #059669; cursor: pointer; transition: all 0.3s;">⭐</button>
                                <button style="padding: 12px; border: 1px solid rgba(16, 185, 129, 0.2); border-radius: 10px; background: rgba(16, 185, 129, 0.1); color: #059669; cursor: pointer; transition: all 0.3s;">📤</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设置页面 -->
                <div id="settings-content" class="page-content">
                    <!-- 页面头部 -->
                    <div class="settings-page-header" style="background: linear-gradient(135deg, #64748b 0%, #475569 100%); border-radius: 16px; padding: 30px; margin-bottom: 30px; color: white; position: relative; overflow: hidden;">
                        <!-- 装饰性背景元素 -->
                        <div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.6;"></div>
                        <div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.08); border-radius: 50%;"></div>

                        <div style="position: relative; z-index: 1;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <div>
                                    <h2 style="font-size: 28px; font-weight: 700; margin-bottom: 8px; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">个人设置</h2>
                                    <p style="font-size: 16px; opacity: 0.9; margin: 0;">管理您的账户信息和偏好设置</p>
                                </div>
                                <button class="btn btn-primary" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 12px 24px; font-size: 14px; font-weight: 600; border-radius: 12px; backdrop-filter: blur(10px); transition: all 0.3s; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">💾 保存设置</button>
                            </div>

                            <!-- 统计信息 -->
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 20px; margin-top: 20px;">
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">30</div>
                                    <div style="font-size: 12px; opacity: 0.8;">学习天数</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">85%</div>
                                    <div style="font-size: 12px; opacity: 0.8;">完成度</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">12</div>
                                    <div style="font-size: 12px; opacity: 0.8;">登录次数</div>
                                </div>
                                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 16px; text-align: center; backdrop-filter: blur(10px);">
                                    <div style="font-size: 24px; font-weight: 700; margin-bottom: 4px;">5</div>
                                    <div style="font-size: 12px; opacity: 0.8;">消息通知</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 设置内容 -->
                    <div class="settings-content" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 24px;">
                        <!-- 个人信息设置 -->
                        <div class="settings-card" style="background: white; border-radius: 16px; padding: 24px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
                            <h3 style="font-size: 18px; color: #1e293b; margin-bottom: 20px; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                                <span style="font-size: 20px;">👤</span>
                                个人信息
                            </h3>

                            <div class="form-group" style="margin-bottom: 20px;">
                                <label style="display: block; font-size: 14px; color: #374151; margin-bottom: 8px; font-weight: 500;">姓名</label>
                                <input type="text" value="李同学" style="width: 100%; padding: 12px 16px; border: 1px solid #e2e8f0; border-radius: 8px; font-size: 14px; background: white;">
                            </div>

                            <div class="form-group" style="margin-bottom: 20px;">
                                <label style="display: block; font-size: 14px; color: #374151; margin-bottom: 8px; font-weight: 500;">学号</label>
                                <input type="text" value="2021001234" style="width: 100%; padding: 12px 16px; border: 1px solid #e2e8f0; border-radius: 8px; font-size: 14px; background: #f8fafc;" readonly>
                            </div>

                            <div class="form-group" style="margin-bottom: 20px;">
                                <label style="display: block; font-size: 14px; color: #374151; margin-bottom: 8px; font-weight: 500;">邮箱</label>
                                <input type="email" value="<EMAIL>" style="width: 100%; padding: 12px 16px; border: 1px solid #e2e8f0; border-radius: 8px; font-size: 14px; background: white;">
                            </div>

                            <div class="form-group">
                                <label style="display: block; font-size: 14px; color: #374151; margin-bottom: 8px; font-weight: 500;">专业</label>
                                <select style="width: 100%; padding: 12px 16px; border: 1px solid #e2e8f0; border-radius: 8px; font-size: 14px; background: white;">
                                    <option>土木工程</option>
                                    <option>建筑学</option>
                                    <option>工程管理</option>
                                </select>
                            </div>
                        </div>

                        <!-- 学习偏好设置 -->
                        <div class="settings-card" style="background: white; border-radius: 16px; padding: 24px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
                            <h3 style="font-size: 18px; color: #1e293b; margin-bottom: 20px; font-weight: 600; display: flex; align-items: center; gap: 8px;">
                                <span style="font-size: 20px;">⚙️</span>
                                学习偏好
                            </h3>

                            <div class="form-group" style="margin-bottom: 20px;">
                                <label style="display: block; font-size: 14px; color: #374151; margin-bottom: 8px; font-weight: 500;">学习提醒</label>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <input type="checkbox" checked style="width: 16px; height: 16px;">
                                    <span style="font-size: 14px; color: #64748b;">开启每日学习提醒</span>
                                </div>
                            </div>

                            <div class="form-group" style="margin-bottom: 20px;">
                                <label style="display: block; font-size: 14px; color: #374151; margin-bottom: 8px; font-weight: 500;">作业截止提醒</label>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <input type="checkbox" checked style="width: 16px; height: 16px;">
                                    <span style="font-size: 14px; color: #64748b;">作业截止前24小时提醒</span>
                                </div>
                            </div>

                            <div class="form-group" style="margin-bottom: 20px;">
                                <label style="display: block; font-size: 14px; color: #374151; margin-bottom: 8px; font-weight: 500;">讨论回复通知</label>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <input type="checkbox" style="width: 16px; height: 16px;">
                                    <span style="font-size: 14px; color: #64748b;">有人回复我的讨论时通知</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label style="display: block; font-size: 14px; color: #374151; margin-bottom: 8px; font-weight: 500;">主题模式</label>
                                <select style="width: 100%; padding: 12px 16px; border: 1px solid #e2e8f0; border-radius: 8px; font-size: 14px; background: white;">
                                    <option>浅色模式</option>
                                    <option>深色模式</option>
                                    <option>跟随系统</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 页面内容数据
        const contentData = {
            course: {
                title: '我的课程',
                description: '查看和管理您的学习课程'
            },
            knowledge: {
                title: '知识图谱',
                description: '可视化学习路径和知识结构'
            },
            homework: {
                title: '我的作业',
                description: '查看和完成课程作业'
            },
            discussion: {
                title: '课程讨论',
                description: '参与课程讨论和交流'
            },
            group: {
                title: '小组协作',
                description: '与同学协作学习和项目管理'
            },
            ai: {
                title: 'AI智能助手',
                description: '智能学习伙伴，随时答疑解惑'
            },
            resources: {
                title: '教师共享资料',
                description: '获取优质教学资源'
            },
            settings: {
                title: '个人设置',
                description: '管理账户信息和偏好设置'
            }
        };

        // 菜单切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.menu-item');
            const contentTitle = document.getElementById('content-title');
            const contentDescription = document.getElementById('content-description');
            const pageContents = document.querySelectorAll('.page-content');

            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 移除所有活动状态
                    menuItems.forEach(mi => mi.classList.remove('active'));
                    pageContents.forEach(pc => pc.classList.remove('active'));

                    // 添加当前项的活动状态
                    this.classList.add('active');

                    // 获取内容类型
                    const contentType = this.getAttribute('data-content');
                    const targetContent = document.getElementById(contentType + '-content');

                    if (targetContent) {
                        targetContent.classList.add('active');
                    }

                    // 更新标题和描述
                    if (contentData[contentType]) {
                        contentTitle.textContent = contentData[contentType].title;
                        contentDescription.textContent = contentData[contentType].description;
                    }
                });
            });
        });
    </script>
</body>
</html>
