:root {
  /* 颜色 */
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --accent-color: #9b59b6;
  --text-color: #333333;
  --text-light: #666666;
  --text-lighter: #999999;
  --background-color: #ffffff;
  --background-light: #f5f5f5;
  --border-color: #e0e0e0;
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --error-color: #e74c3c;
  --info-color: #3498db;

  /* 字体 */
  --font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  --font-size-small: 12px;
  --font-size-normal: 14px;
  --font-size-medium: 16px;
  --font-size-large: 18px;
  --font-size-xlarge: 20px;
  --font-size-xxlarge: 24px;

  /* 间距 - 极度紧凑模式 */
  --spacing-xs: 2px;
  --spacing-sm: 4px;
  --spacing-md: 6px;
  --spacing-lg: 8px;
  --spacing-xl: 10px;
  --spacing-xxl: 12px;

  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-round: 50%;

  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);

  /* 过渡 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  /* 布局 */
  --header-height: 60px;
  --footer-height: 80px;
  --sidebar-width: 240px;
  --container-max-width: 1200px;
} 