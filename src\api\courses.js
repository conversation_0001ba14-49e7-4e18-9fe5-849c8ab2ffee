import request from './axios';

/**
 * 获取课程列表
 * @param {Object} params 查询参数
 * @param {string} params.createBy 创建者
 * @param {string} params.createTime 创建时间
 * @param {string} params.updateBy 更新者
 * @param {string} params.updateTime 更新时间
 * @param {string} params.remark 备注
 * @param {Object} params.params 其他参数
 * @param {number} params.id 课程ID
 * @param {string} params.name 课程名称
 * @param {string} params.description 课程描述
 * @param {number} params.deptId 部门ID
 * @param {number} params.teacherId 教师ID
 * @param {number} params.tpId 模板ID
 * @returns {Promise<Object>} 课程列表响应数据
 */
export const getCoursesList = (params = {}) => {
  console.log('正在获取课程列表...', params);

  // 构建查询参数，过滤掉空值
  const cleanParams = {};
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
      cleanParams[key] = params[key];
    }
  });

  return request({
    url: '/core/courses/list',
    method: 'get',
    params: cleanParams,
    // 使用表单编码格式，与接口文档保持一致
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log('获取课程列表成功:', response);

    // 检查响应格式是否符合预期的TableDataInfo结构
    if (response && typeof response === 'object') {
      // 标准响应格式：{ total, rows, code, msg }
      if (response.hasOwnProperty('total') && response.hasOwnProperty('rows')) {
        console.log('返回标准TableDataInfo格式');
        return response;
      }
      // 如果直接返回数组，包装成标准格式
      else if (Array.isArray(response)) {
        console.log('返回数组格式，包装成标准格式');
        return {
          total: response.length,
          rows: response,
          code: 0,
          msg: '成功'
        };
      }
      // 如果有data字段且是数组，提取数据
      else if (response.data && Array.isArray(response.data)) {
        console.log('从嵌套响应中提取课程列表数据');
        return {
          total: response.data.length,
          rows: response.data,
          code: response.code || 0,
          msg: response.msg || '成功'
        };
      }
    }

    console.warn('响应格式不符合预期，返回空结果');
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: '响应格式不符合预期'
    };
  }).catch(error => {
    console.error('获取课程列表失败:', error);
    // 返回错误格式，保持与TableDataInfo一致
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: error.message || '获取课程列表失败'
    };
  });
};

/**
 * 新增课程
 * @param {Object} courseData 课程数据
 * @param {string} courseData.createBy 创建者
 * @param {string} courseData.createTime 创建时间
 * @param {string} courseData.updateBy 更新者
 * @param {string} courseData.updateTime 更新时间
 * @param {string} courseData.remark 备注
 * @param {Object} courseData.params 其他参数
 * @param {number} courseData.classId 关联的班级ID（直接参数）
 * @param {number} courseData.id 课程ID
 * @param {string} courseData.name 课程名称
 * @param {string} courseData.description 课程描述
 * @param {number} courseData.deptId 部门ID
 * @param {number} courseData.teacherId 教师ID
 * @param {number} courseData.tpId 模板ID
 * @returns {Promise<Object>} 创建结果
 */
export const createCourse = (courseData) => {
  console.log('正在创建新课程...', courseData);
  console.log('原始课程数据JSON:', JSON.stringify(courseData, null, 2));

  // 构建请求数据，过滤掉空值，但保留params对象和班级相关字段
  const cleanData = {};
  Object.keys(courseData).forEach(key => {
    if (key === 'params') {
      // params对象特殊处理，即使为空对象也保留
      cleanData[key] = courseData[key] || {};
    } else if (key.toLowerCase().includes('class') || key === 'clClasses') {
      // 班级相关字段特殊处理，保留所有可能的格式
      cleanData[key] = courseData[key];
    } else if (courseData[key] !== null && courseData[key] !== undefined && courseData[key] !== '') {
      cleanData[key] = courseData[key];
    }
  });

  console.log('清理后的课程数据:', cleanData);
  console.log('清理后的课程数据JSON:', JSON.stringify(cleanData, null, 2));

  console.log('即将发送HTTP请求到 /core/courses');
  console.log('请求方法: POST');
  console.log('请求头: Content-Type: application/json');
  console.log('请求体:', JSON.stringify(cleanData, null, 2));

  return request({
    url: '/core/courses',
    method: 'post',
    data: cleanData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('创建课程HTTP响应成功:', response);
    console.log('响应类型:', typeof response);
    console.log('响应JSON:', JSON.stringify(response, null, 2));

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 根据接口文档，响应包含 error, success, warn, empty 字段
      if (response.hasOwnProperty('success')) {
        console.log('响应包含success字段:', response.success);
        return response;
      }
    }

    console.warn('创建课程响应格式不符合预期:', response);
    return response;
  }).catch(error => {
    console.error('创建课程HTTP请求失败:', error);
    console.error('错误类型:', typeof error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    if (error.response) {
      console.error('错误响应状态:', error.response.status);
      console.error('错误响应数据:', error.response.data);
    }
    return Promise.reject(error);
  });
};

/**
 * 根据ID获取课程详情
 * @param {string|number} courseId 课程ID
 * @returns {Promise<Object>} 课程详情数据
 */
export const getCourseById = (courseId) => {
  console.log(`正在获取课程详情: ${courseId}`);
  return request({
    url: `/core/courses/${courseId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取课程${courseId}详情成功:`, response);
    return response;
  }).catch(error => {
    console.error(`获取课程 ${courseId} 详情失败:`, error);
    return Promise.reject(error);
  });
};